using UnityEngine;
using System.Collections.Generic;
using System.Linq;

/// <summary>
/// 紧固件使用状态跟踪器
/// 用于调试和验证紧固件的使用情况
/// </summary>
public class FastenerUsageTracker : MonoBehaviour
{
    [Header("调试设置")]
    [SerializeField] private bool enableDebugMode = true;
    [SerializeField] private KeyCode debugKey = KeyCode.F2;
    
    [Header("容器引用")]
    [SerializeField] private Transform screwsContainer;
    [SerializeField] private Transform nutsContainer;
    
    // 跟踪数据
    private Dictionary<string, FastenerUsageInfo> fastenerUsageHistory = new Dictionary<string, FastenerUsageInfo>();
    
    /// <summary>
    /// 紧固件使用信息
    /// </summary>
    [System.Serializable]
    public struct FastenerUsageInfo
    {
        public string fastenerName;
        public string fastenerType;
        public bool isCurrentlyUsed;
        public int usageCount;
        public Vector3 lastPosition;
        public string lastUsedInStep;
        public float lastUsedTime;
        
        public FastenerUsageInfo(string name, string type)
        {
            fastenerName = name;
            fastenerType = type;
            isCurrentlyUsed = false;
            usageCount = 0;
            lastPosition = Vector3.zero;
            lastUsedInStep = "";
            lastUsedTime = 0f;
        }
    }
    
    void Update()
    {
        if (enableDebugMode && Input.GetKeyDown(debugKey))
        {
            ShowFastenerUsageReport();
        }
    }
    
    /// <summary>
    /// 显示紧固件使用报告
    /// </summary>
    public void ShowFastenerUsageReport()
    {
        Debug.Log("🔍 ===== 紧固件使用状态报告 =====");
        
        UpdateFastenerUsageInfo();
        
        // 按类型分组显示
        var screwTypes = new Dictionary<string, List<FastenerUsageInfo>>();
        var nutTypes = new Dictionary<string, List<FastenerUsageInfo>>();
        
        foreach (var kvp in fastenerUsageHistory)
        {
            var info = kvp.Value;
            if (info.fastenerName.Contains("Screw"))
            {
                string type = ExtractFastenerType(info.fastenerName, "Screw");
                if (!screwTypes.ContainsKey(type))
                    screwTypes[type] = new List<FastenerUsageInfo>();
                screwTypes[type].Add(info);
            }
            else if (info.fastenerName.Contains("Nut"))
            {
                string type = ExtractFastenerType(info.fastenerName, "Nut");
                if (!nutTypes.ContainsKey(type))
                    nutTypes[type] = new List<FastenerUsageInfo>();
                nutTypes[type].Add(info);
            }
        }
        
        // 显示螺丝使用情况
        Debug.Log("🔩 螺丝使用情况:");
        foreach (var typeGroup in screwTypes)
        {
            string type = typeGroup.Key;
            var fasteners = typeGroup.Value;
            int usedCount = fasteners.Count(f => f.isCurrentlyUsed);
            int totalCount = fasteners.Count;
            
            Debug.Log($"   {type}: {usedCount}/{totalCount} 已使用");
            
            foreach (var fastener in fasteners)
            {
                string status = fastener.isCurrentlyUsed ? "✅已使用" : "⭕可用";
                Debug.Log($"     {fastener.fastenerName}: {status} (使用次数: {fastener.usageCount})");
            }
        }
        
        // 显示螺母使用情况
        Debug.Log("🔧 螺母使用情况:");
        foreach (var typeGroup in nutTypes)
        {
            string type = typeGroup.Key;
            var fasteners = typeGroup.Value;
            int usedCount = fasteners.Count(f => f.isCurrentlyUsed);
            int totalCount = fasteners.Count;
            
            Debug.Log($"   {type}: {usedCount}/{totalCount} 已使用");
            
            foreach (var fastener in fasteners)
            {
                string status = fastener.isCurrentlyUsed ? "✅已使用" : "⭕可用";
                Debug.Log($"     {fastener.fastenerName}: {status} (使用次数: {fastener.usageCount})");
            }
        }
        
        Debug.Log("🔍 ===== 报告结束 =====");
    }
    
    /// <summary>
    /// 更新紧固件使用信息
    /// </summary>
    private void UpdateFastenerUsageInfo()
    {
        // 检查螺丝容器
        if (screwsContainer != null)
        {
            foreach (Transform child in screwsContainer)
            {
                UpdateSingleFastenerInfo(child, "Screw");
            }
        }
        
        // 检查螺母容器
        if (nutsContainer != null)
        {
            foreach (Transform child in nutsContainer)
            {
                UpdateSingleFastenerInfo(child, "Nut");
            }
        }
    }
    
    /// <summary>
    /// 更新单个紧固件信息
    /// </summary>
    private void UpdateSingleFastenerInfo(Transform fastenerTransform, string category)
    {
        string fastenerName = fastenerTransform.name;
        
        if (!fastenerUsageHistory.ContainsKey(fastenerName))
        {
            string type = ExtractFastenerType(fastenerName, category);
            fastenerUsageHistory[fastenerName] = new FastenerUsageInfo(fastenerName, type);
        }
        
        var info = fastenerUsageHistory[fastenerName];
        
        // 检查是否被使用（位置是否发生显著变化）
        Vector3 currentPosition = fastenerTransform.position;
        bool wasUsed = info.isCurrentlyUsed;
        
        // 如果位置发生显著变化，认为被使用了
        if (Vector3.Distance(currentPosition, info.lastPosition) > 0.1f && info.lastPosition != Vector3.zero)
        {
            if (!wasUsed)
            {
                info.usageCount++;
                info.lastUsedTime = Time.time;
                info.isCurrentlyUsed = true;
                
                if (enableDebugMode)
                {
                    Debug.Log($"🔩 检测到紧固件被使用: {fastenerName}");
                }
            }
        }
        
        info.lastPosition = currentPosition;
        fastenerUsageHistory[fastenerName] = info;
    }
    
    /// <summary>
    /// 提取紧固件类型
    /// </summary>
    private string ExtractFastenerType(string fastenerName, string category)
    {
        if (fastenerName.StartsWith(category))
        {
            string type = fastenerName.Replace(category, "");
            // 移除数字后缀（如_1, _2等）
            int underscoreIndex = type.LastIndexOf('_');
            if (underscoreIndex > 0)
            {
                type = type.Substring(0, underscoreIndex);
            }
            return type;
        }
        return "Unknown";
    }
    
    /// <summary>
    /// 重置使用状态跟踪
    /// </summary>
    public void ResetUsageTracking()
    {
        foreach (var key in fastenerUsageHistory.Keys.ToList())
        {
            var info = fastenerUsageHistory[key];
            info.isCurrentlyUsed = false;
            fastenerUsageHistory[key] = info;
        }
        
        if (enableDebugMode)
        {
            Debug.Log("🔄 重置紧固件使用状态跟踪");
        }
    }
    
    /// <summary>
    /// 获取指定类型的可用紧固件数量
    /// </summary>
    public int GetAvailableFastenerCount(string fastenerType)
    {
        UpdateFastenerUsageInfo();
        
        int count = 0;
        foreach (var kvp in fastenerUsageHistory)
        {
            var info = kvp.Value;
            if (info.fastenerType == fastenerType && !info.isCurrentlyUsed)
            {
                count++;
            }
        }
        
        return count;
    }
    
    /// <summary>
    /// 检查是否有足够的可用紧固件
    /// </summary>
    public bool HasEnoughFasteners(string fastenerType, int requiredCount)
    {
        return GetAvailableFastenerCount(fastenerType) >= requiredCount;
    }
}
