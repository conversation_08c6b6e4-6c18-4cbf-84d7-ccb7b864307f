using UnityEngine;

/// <summary>
/// PICO部署管理器
/// 用于在开发环境和PICO部署环境之间切换配置
/// </summary>
public class PICODeploymentManager : MonoBehaviour
{
    [Header("部署配置")]
    [SerializeField] private bool isPICODeployment = false; // 是否为PICO部署模式
    [SerializeField] private bool autoDetectPlatform = true; // 自动检测平台

    [Header("组件引用")]
    [SerializeField] private Neo4jAssemblyController assemblyController;
    [SerializeField] private SimpleAssemblyDataReceiver dataReceiver;

    [Header("开发环境设置")]
    [SerializeField] private bool enableDebugInDevelopment = true;
    [SerializeField] private bool useNeo4jInDevelopment = true;

    [Header("PICO环境设置")]
    [SerializeField] private bool enableVRInPICO = true;
    [SerializeField] private bool useExternalDataInPICO = true;

    void Awake()
    {
        // 在其他组件初始化之前配置环境
        ConfigureEnvironment();
    }

    void Start()
    {
        // 验证配置
        ValidateConfiguration();
        
        // 显示当前配置信息
        LogCurrentConfiguration();
    }

    /// <summary>
    /// 配置运行环境
    /// </summary>
    private void ConfigureEnvironment()
    {
        // 自动检测平台
        if (autoDetectPlatform)
        {
            DetectPlatform();
        }

        // 查找必要组件
        FindComponents();

        // 根据部署模式配置系统
        if (isPICODeployment)
        {
            ConfigureForPICO();
        }
        else
        {
            ConfigureForDevelopment();
        }
    }

    /// <summary>
    /// 自动检测平台
    /// </summary>
    private void DetectPlatform()
    {
        // 检测是否在Android平台运行（PICO设备）
        bool isAndroid = Application.platform == RuntimePlatform.Android;
        
        // 检测是否有VR设备
        bool hasVRDevice = false;
#if UNITY_XR_INTERACTION_TOOLKIT
        try
        {
            hasVRDevice = UnityEngine.XR.XRSettings.isDeviceActive;
        }
        catch
        {
            hasVRDevice = false;
        }
#endif

        // 如果是Android且有VR设备，可能是PICO部署
        if (isAndroid && hasVRDevice)
        {
            isPICODeployment = true;
            Debug.Log("[PICODeploymentManager] 自动检测到PICO部署环境");
        }
        else
        {
            isPICODeployment = false;
            Debug.Log("[PICODeploymentManager] 自动检测到开发环境");
        }
    }

    /// <summary>
    /// 查找必要组件
    /// </summary>
    private void FindComponents()
    {
        if (assemblyController == null)
        {
            assemblyController = FindObjectOfType<Neo4jAssemblyController>();
        }

        if (dataReceiver == null)
        {
            dataReceiver = FindObjectOfType<SimpleAssemblyDataReceiver>();
        }
    }

    /// <summary>
    /// 配置PICO部署环境
    /// </summary>
    private void ConfigureForPICO()
    {
        Debug.Log("[PICODeploymentManager] 配置PICO部署环境");

        if (assemblyController != null)
        {
            // 启用VR模式
            SetPrivateField(assemblyController, "enableVRMode", enableVRInPICO);
            
            // 启用外部数据源
            SetPrivateField(assemblyController, "useExternalDataSource", useExternalDataInPICO);
            
            Debug.Log($"[PICODeploymentManager] VR模式: {enableVRInPICO}, 外部数据源: {useExternalDataInPICO}");
        }

        if (dataReceiver != null)
        {
            // 启用数据接收器
            dataReceiver.enabled = true;
            SetPrivateField(dataReceiver, "enableDebugMode", false); // PICO环境下减少日志
        }

        // 优化PICO性能设置
        OptimizeForPICO();
    }

    /// <summary>
    /// 配置开发环境
    /// </summary>
    private void ConfigureForDevelopment()
    {
        Debug.Log("[PICODeploymentManager] 配置开发环境");

        if (assemblyController != null)
        {
            // 保持传统模式
            SetPrivateField(assemblyController, "enableVRMode", false);
            
            // 使用Neo4j数据源
            SetPrivateField(assemblyController, "useExternalDataSource", !useNeo4jInDevelopment);
            
            Debug.Log($"[PICODeploymentManager] VR模式: false, Neo4j数据源: {useNeo4jInDevelopment}");
        }

        if (dataReceiver != null)
        {
            // 数据接收器可选启用（用于测试）
            dataReceiver.enabled = true;
            SetPrivateField(dataReceiver, "enableDebugMode", enableDebugInDevelopment);
        }
    }

    /// <summary>
    /// 优化PICO性能设置
    /// </summary>
    private void OptimizeForPICO()
    {
        // 设置目标帧率
        Application.targetFrameRate = 90; // PICO 4的推荐帧率

        // 优化质量设置
        QualitySettings.vSyncCount = 0; // 禁用垂直同步，让XR系统控制
        
        // 减少阴影质量以提高性能
        QualitySettings.shadowResolution = ShadowResolution.Low;
        QualitySettings.shadowDistance = 10f;

        Debug.Log("[PICODeploymentManager] PICO性能优化完成");
    }

    /// <summary>
    /// 使用反射设置私有字段（临时解决方案）
    /// </summary>
    private void SetPrivateField(object obj, string fieldName, object value)
    {
        try
        {
            var field = obj.GetType().GetField(fieldName, 
                System.Reflection.BindingFlags.NonPublic | 
                System.Reflection.BindingFlags.Instance);
            
            if (field != null)
            {
                field.SetValue(obj, value);
            }
            else
            {
                Debug.LogWarning($"[PICODeploymentManager] 未找到字段: {fieldName}");
            }
        }
        catch (System.Exception e)
        {
            Debug.LogError($"[PICODeploymentManager] 设置字段失败: {fieldName}, 错误: {e.Message}");
        }
    }

    /// <summary>
    /// 验证配置
    /// </summary>
    private void ValidateConfiguration()
    {
        if (assemblyController == null)
        {
            Debug.LogWarning("[PICODeploymentManager] 未找到Neo4jAssemblyController组件");
        }

        if (isPICODeployment && dataReceiver == null)
        {
            Debug.LogWarning("[PICODeploymentManager] PICO部署模式下建议配置SimpleAssemblyDataReceiver");
        }
    }

    /// <summary>
    /// 记录当前配置信息
    /// </summary>
    private void LogCurrentConfiguration()
    {
        Debug.Log($"=== PICO部署管理器配置信息 ===");
        Debug.Log($"部署模式: {(isPICODeployment ? "PICO部署" : "开发环境")}");
        Debug.Log($"平台: {Application.platform}");
        Debug.Log($"自动检测: {autoDetectPlatform}");
        
        if (assemblyController != null)
        {
            Debug.Log($"装配控制器: 已找到");
        }
        
        if (dataReceiver != null)
        {
            Debug.Log($"数据接收器: 已找到");
        }
        
        Debug.Log($"==============================");
    }

    /// <summary>
    /// 手动切换到PICO模式（用于测试）
    /// </summary>
    [ContextMenu("切换到PICO模式")]
    public void SwitchToPICOMode()
    {
        isPICODeployment = true;
        ConfigureForPICO();
        LogCurrentConfiguration();
        Debug.Log("[PICODeploymentManager] 已手动切换到PICO模式");
    }

    /// <summary>
    /// 手动切换到开发模式
    /// </summary>
    [ContextMenu("切换到开发模式")]
    public void SwitchToDevelopmentMode()
    {
        isPICODeployment = false;
        ConfigureForDevelopment();
        LogCurrentConfiguration();
        Debug.Log("[PICODeploymentManager] 已手动切换到开发模式");
    }

    /// <summary>
    /// 获取当前部署状态
    /// </summary>
    public bool IsPICODeployment => isPICODeployment;

    /// <summary>
    /// 获取配置信息
    /// </summary>
    public string GetConfigurationInfo()
    {
        bool vrActive = false;
#if UNITY_XR_INTERACTION_TOOLKIT
        try
        {
            vrActive = UnityEngine.XR.XRSettings.isDeviceActive;
        }
        catch
        {
            vrActive = false;
        }
#endif

        return $"模式: {(isPICODeployment ? "PICO" : "开发")}, " +
               $"平台: {Application.platform}, " +
               $"VR设备: {(vrActive ? "已连接" : "未连接")}";
    }
}
