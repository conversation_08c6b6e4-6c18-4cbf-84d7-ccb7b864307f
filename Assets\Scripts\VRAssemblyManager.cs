using UnityEngine;
using System.Collections;

/// <summary>
/// VR装配管理器
/// 
/// 整合VR位置调整、预览系统和用户引导，
/// 为VR环境提供完整的装配动画展示解决方案
/// </summary>
public class VRAssemblyManager : MonoBehaviour
{
    [Header("核心组件")]
    [SerializeField] private VRAssemblyPositioner positioner;
    [SerializeField] private VRAssemblyPreview preview;
    [SerializeField] private VRUserGuidance guidance;
    [SerializeField] private Neo4jAssemblyController assemblyController;
    
    [Header("VR设置")]
    [SerializeField] private bool enableVRMode = true;
    [SerializeField] private bool autoPositioning = true;
    [SerializeField] private bool enablePreview = true;
    [SerializeField] private bool enableGuidance = true;
    
    [Header("装配流程设置")]
    [SerializeField] private float stepDelay = 1f;
    [SerializeField] private bool waitForUserConfirmation = false;
    [SerializeField] private KeyCode confirmationKey = KeyCode.Space;
    
    // 内部状态
    private bool isVRAssemblyActive = false;
    private bool isVRPreparationComplete = false;
    private AssemblyPart currentMovingPart;
    private AssemblyPart currentTargetPart;

    void Start()
    {
        InitializeVRAssembly();
    }

    /// <summary>
    /// 初始化VR装配系统
    /// </summary>
    private void InitializeVRAssembly()
    {
        // 自动查找组件
        if (positioner == null)
            positioner = GetComponent<VRAssemblyPositioner>();
        
        if (preview == null)
            preview = GetComponent<VRAssemblyPreview>();
        
        if (guidance == null)
            guidance = GetComponent<VRUserGuidance>();
        
        if (assemblyController == null)
            assemblyController = FindObjectOfType<Neo4jAssemblyController>();

        // 检查必要组件
        ValidateComponents();

        // 注册事件
        RegisterEvents();

        Debug.Log($"[VRAssemblyManager] VR装配系统初始化完成 (VR模式: {enableVRMode})");
    }

    /// <summary>
    /// 验证必要组件
    /// </summary>
    private void ValidateComponents()
    {
        if (enableVRMode)
        {
            if (positioner == null && autoPositioning)
            {
                Debug.LogWarning("[VRAssemblyManager] VRAssemblyPositioner组件未找到，自动定位功能将被禁用");
                autoPositioning = false;
            }

            if (preview == null && enablePreview)
            {
                Debug.LogWarning("[VRAssemblyManager] VRAssemblyPreview组件未找到，预览功能将被禁用");
                enablePreview = false;
            }

            if (guidance == null && enableGuidance)
            {
                Debug.LogWarning("[VRAssemblyManager] VRUserGuidance组件未找到，引导功能将被禁用");
                enableGuidance = false;
            }
        }

        if (assemblyController == null)
        {
            Debug.LogError("[VRAssemblyManager] Neo4jAssemblyController未找到，VR装配系统无法正常工作");
        }
    }

    /// <summary>
    /// 注册事件
    /// </summary>
    private void RegisterEvents()
    {
        if (assemblyController != null)
        {
            // 这里可以注册装配控制器的事件
            // 例如：assemblyController.OnStepExecuted += OnAssemblyStepExecuted;
        }
    }

    /// <summary>
    /// 开始VR装配流程
    /// </summary>
    public void StartVRAssembly()
    {
        if (!enableVRMode || isVRAssemblyActive)
        {
            Debug.LogWarning("[VRAssemblyManager] VR模式未启用或装配已在进行中");
            return;
        }

        StartCoroutine(VRAssemblySequence());
    }

    /// <summary>
    /// VR装配序列协程
    /// </summary>
    private IEnumerator VRAssemblySequence()
    {
        isVRAssemblyActive = true;

        Debug.Log("[VRAssemblyManager] 开始VR装配序列");

        // 1. 初始位置调整
        if (autoPositioning && positioner != null)
        {
            Debug.Log("[VRAssemblyManager] 调整装配区域到最佳位置");
            positioner.OnAssemblyStart();
            yield return new WaitForSeconds(stepDelay);
        }

        // 2. 高亮装配区域
        if (enableGuidance && guidance != null)
        {
            Transform assemblyRoot = positioner?.transform;
            if (assemblyRoot != null)
            {
                guidance.HighlightAssemblyArea(assemblyRoot, 2f);
            }
        }

        // 3. 等待用户确认或自动继续
        if (waitForUserConfirmation)
        {
            yield return StartCoroutine(WaitForUserConfirmation("按空格键开始装配"));
        }
        else
        {
            yield return new WaitForSeconds(stepDelay);
        }

        Debug.Log("[VRAssemblyManager] VR装配序列准备完成");
        isVRAssemblyActive = false;
    }

    /// <summary>
    /// 装配步骤开始时的VR处理
    /// </summary>
    public void OnAssemblyStepStart(AssemblyPart movingPart, AssemblyPart targetPart, string stepDescription = "")
    {
        if (!enableVRMode) return;

        currentMovingPart = movingPart;
        currentTargetPart = targetPart;

        StartCoroutine(HandleVRAssemblyStep(movingPart, targetPart, stepDescription));
    }

    /// <summary>
    /// 处理VR装配步骤（修复时序问题）
    /// </summary>
    private IEnumerator HandleVRAssemblyStep(AssemblyPart movingPart, AssemblyPart targetPart, string stepDescription)
    {
        Debug.Log($"[VRAssemblyManager] 开始VR装配步骤: {stepDescription}");

        // === 第一阶段：位置和朝向调整（必须完成后再继续） ===
        if (autoPositioning && positioner != null)
        {
            Debug.Log("[VRAssemblyManager] 第一阶段：调整装配区域位置和朝向");

            // 等待位置调整完成
            yield return StartCoroutine(positioner.AdjustOrientationForAssemblyStep(movingPart, targetPart));

            Debug.Log("[VRAssemblyManager] 位置和朝向调整完成");
        }

        // === 第二阶段：创建预览和引导（基于调整后的位置） ===
        Debug.Log("[VRAssemblyManager] 第二阶段：创建预览和用户引导");

        // 2. 创建预览（基于调整后的位置）
        if (enablePreview && preview != null)
        {
            preview.CreateStepPreview(movingPart, targetPart, stepDescription);
            Debug.Log("[VRAssemblyManager] 预览创建完成");
        }

        // 3. 用户引导（基于调整后的位置判断）
        if (enableGuidance && guidance != null && ShouldProvideGuidance(movingPart, targetPart))
        {
            Vector3 optimalPosition = CalculateOptimalViewingPosition(movingPart, targetPart);

            // 等待用户引导完成
            yield return StartCoroutine(guidance.GuideUserToOptimalPosition(optimalPosition, $"装配步骤: {stepDescription}"));

            Debug.Log("[VRAssemblyManager] 用户引导完成");
        }

        // === 第三阶段：VR准备完成，可以开始装配动画 ===
        Debug.Log("[VRAssemblyManager] VR准备完成，装配动画可以开始");

        // 标记VR准备完成
        isVRPreparationComplete = true;

        // 短暂延迟确保所有VR效果都已就位
        yield return new WaitForSeconds(0.2f);
    }

    /// <summary>
    /// 检查VR准备是否完成
    /// </summary>
    public bool IsVRPreparationComplete => isVRPreparationComplete;

    /// <summary>
    /// 等待VR准备完成
    /// </summary>
    public IEnumerator WaitForVRPreparationComplete()
    {
        isVRPreparationComplete = false;

        // 等待VR准备完成
        while (!isVRPreparationComplete)
        {
            yield return new WaitForSeconds(0.1f);
        }

        Debug.Log("[VRAssemblyManager] VR准备完成确认");
    }

    /// <summary>
    /// 装配步骤结束时的VR处理
    /// </summary>
    public void OnAssemblyStepEnd()
    {
        if (!enableVRMode) return;

        // 清理预览
        if (enablePreview && preview != null)
        {
            preview.OnAssemblyStepEnd();
        }

        // 停止引导
        if (enableGuidance && guidance != null)
        {
            guidance.StopCurrentGuidance();
        }

        currentMovingPart = null;
        currentTargetPart = null;
    }

    /// <summary>
    /// 判断是否需要提供用户引导
    /// </summary>
    private bool ShouldProvideGuidance(AssemblyPart movingPart, AssemblyPart targetPart)
    {
        // 这里可以添加更复杂的逻辑来判断是否需要引导
        // 例如：检查装配点是否在用户视野范围内
        
        if (targetPart == null) return false;

        Transform vrCamera = Camera.main?.transform;
        if (vrCamera == null) return false;

        Vector3 assemblyPoint = targetPart.GetReferencePoint(0).position;
        Vector3 cameraPosition = vrCamera.position;
        Vector3 cameraForward = vrCamera.forward;

        // 计算装配点是否在摄像机前方
        Vector3 toAssemblyPoint = (assemblyPoint - cameraPosition).normalized;
        float dot = Vector3.Dot(cameraForward, toAssemblyPoint);

        // 如果装配点在摄像机后方或侧方，需要引导
        return dot < 0.5f;
    }

    /// <summary>
    /// 计算最佳观看位置
    /// </summary>
    private Vector3 CalculateOptimalViewingPosition(AssemblyPart movingPart, AssemblyPart targetPart)
    {
        if (targetPart == null) return Vector3.zero;

        Vector3 assemblyPoint = targetPart.GetReferencePoint(0).position;
        
        // 简单计算：装配点前方2米处
        Vector3 optimalPosition = assemblyPoint + Vector3.forward * 2f;
        optimalPosition.y = 1.2f; // 适合的观看高度

        return optimalPosition;
    }

    /// <summary>
    /// 等待用户确认
    /// </summary>
    private IEnumerator WaitForUserConfirmation(string message)
    {
        Debug.Log($"[VRAssemblyManager] {message}");

        // 显示确认消息
        if (enableGuidance && guidance != null)
        {
            guidance.PlayVoiceGuidance(message);
        }

        // 等待用户按键
        while (!Input.GetKeyDown(confirmationKey))
        {
            yield return null;
        }

        Debug.Log("[VRAssemblyManager] 用户确认完成");
    }

    /// <summary>
    /// 设置VR模式
    /// </summary>
    public void SetVRMode(bool enabled)
    {
        enableVRMode = enabled;
        Debug.Log($"[VRAssemblyManager] VR模式设置为: {enabled}");
    }

    /// <summary>
    /// 设置自动定位
    /// </summary>
    public void SetAutoPositioning(bool enabled)
    {
        autoPositioning = enabled;
        Debug.Log($"[VRAssemblyManager] 自动定位设置为: {enabled}");
    }

    /// <summary>
    /// 设置预览功能
    /// </summary>
    public void SetPreviewEnabled(bool enabled)
    {
        enablePreview = enabled;
        Debug.Log($"[VRAssemblyManager] 预览功能设置为: {enabled}");
    }

    /// <summary>
    /// 设置引导功能
    /// </summary>
    public void SetGuidanceEnabled(bool enabled)
    {
        enableGuidance = enabled;
        Debug.Log($"[VRAssemblyManager] 引导功能设置为: {enabled}");
    }

    /// <summary>
    /// 紧急停止所有VR功能
    /// </summary>
    public void EmergencyStop()
    {
        StopAllCoroutines();
        
        if (preview != null)
            preview.ClearPreview();
        
        if (guidance != null)
            guidance.StopCurrentGuidance();
        
        isVRAssemblyActive = false;
        
        Debug.Log("[VRAssemblyManager] 紧急停止所有VR功能");
    }

    /// <summary>
    /// 获取VR系统状态
    /// </summary>
    public bool IsVRActive => enableVRMode && isVRAssemblyActive;

    void Update()
    {
        // 处理VR模式下的输入
        if (enableVRMode && Input.GetKeyDown(KeyCode.Escape))
        {
            EmergencyStop();
        }
    }

    void OnDestroy()
    {
        EmergencyStop();
    }
}
