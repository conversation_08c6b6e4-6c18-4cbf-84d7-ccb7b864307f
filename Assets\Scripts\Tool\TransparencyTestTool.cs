using UnityEngine;

/// <summary>
/// 透明化功能测试工具
/// 用于测试AgentTools中的高亮和透明化功能
/// </summary>
public class TransparencyTestTool : MonoBehaviour
{
    [Header("测试设置")]
    [SerializeField] private AgentTools agentTools;
    [SerializeField] private string testPartName = "主舵盘1"; // 测试用的零件名称
    [SerializeField] private float transparencyRadius = 5f; // 透明化半径
    
    [Header("控制键设置")]
    [SerializeField] private KeyCode highlightKey = KeyCode.H; // 高亮
    [SerializeField] private KeyCode transparentKey = KeyCode.T; // 透明化
    [SerializeField] private KeyCode combinedKey = KeyCode.Y; // 高亮+透明化
    [SerializeField] private KeyCode restoreKey = KeyCode.U; // 恢复材质
    
    void Start()
    {
        // 自动查找AgentTools组件
        if (agentTools == null)
        {
            agentTools = FindObjectOfType<AgentTools>();
        }
        
        if (agentTools == null)
        {
            Debug.LogError("[TransparencyTestTool] 未找到AgentTools组件！");
            enabled = false;
        }
        
        Debug.Log("[TransparencyTestTool] 透明化测试工具已启动");
        ShowInstructions();
    }
    
    void Update()
    {
        // 高亮测试
        if (Input.GetKeyDown(highlightKey))
        {
            TestHighlight();
        }
        
        // 透明化测试
        if (Input.GetKeyDown(transparentKey))
        {
            TestTransparency();
        }
        
        // 组合功能测试
        if (Input.GetKeyDown(combinedKey))
        {
            TestCombinedFunction();
        }
        
        // 恢复材质测试
        if (Input.GetKeyDown(restoreKey))
        {
            TestRestoreMaterials();
        }
    }
    
    /// <summary>
    /// 测试高亮功能
    /// </summary>
    private void TestHighlight()
    {
        Debug.Log($"🔆 测试高亮功能: {testPartName}");
        agentTools.HighlightPart(testPartName);
    }
    
    /// <summary>
    /// 测试透明化功能
    /// </summary>
    private void TestTransparency()
    {
        Debug.Log($"👻 测试透明化功能: {testPartName} (半径: {transparencyRadius})");
        agentTools.MakePartsTransparent(testPartName, transparencyRadius);
    }
    
    /// <summary>
    /// 测试组合功能（高亮+透明化）
    /// </summary>
    private void TestCombinedFunction()
    {
        Debug.Log($"🌟 测试组合功能: {testPartName} (半径: {transparencyRadius})");
        agentTools.HighlightPartWithTransparency(testPartName, transparencyRadius);
    }
    
    /// <summary>
    /// 测试恢复材质功能
    /// </summary>
    private void TestRestoreMaterials()
    {
        Debug.Log($"🔄 测试恢复材质功能: {testPartName} (半径: {transparencyRadius})");
        agentTools.RestorePartsAroundTarget(testPartName, transparencyRadius);
    }
    
    /// <summary>
    /// 显示操作说明
    /// </summary>
    private void ShowInstructions()
    {
        Debug.Log("=== 透明化测试工具操作说明 ===");
        Debug.Log($"{highlightKey} 键 - 高亮零件 '{testPartName}'");
        Debug.Log($"{transparentKey} 键 - 透明化 '{testPartName}' 周围零件");
        Debug.Log($"{combinedKey} 键 - 高亮 + 透明化组合功能");
        Debug.Log($"{restoreKey} 键 - 恢复 '{testPartName}' 周围零件材质");
        Debug.Log("================================");
    }
    
    void OnGUI()
    {
        GUILayout.BeginArea(new Rect(10, 10, 300, 150));
        GUILayout.Label("透明化测试工具");
        GUILayout.Space(5);
        
        GUILayout.Label($"测试零件: {testPartName}");
        GUILayout.Label($"透明化半径: {transparencyRadius}");
        GUILayout.Space(10);
        
        GUILayout.Label("控制键:");
        GUILayout.Label($"{highlightKey} - 高亮");
        GUILayout.Label($"{transparentKey} - 透明化");
        GUILayout.Label($"{combinedKey} - 高亮+透明化");
        GUILayout.Label($"{restoreKey} - 恢复材质");
        
        GUILayout.EndArea();
    }
}
