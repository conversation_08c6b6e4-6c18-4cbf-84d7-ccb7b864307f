using UnityEngine;
using UnityEngine.UI;
using System.Collections.Generic;
using System.Linq;

/// <summary>
/// 装配调试助手
/// 帮助诊断零件管理和装配系统的问题
/// </summary>
public class AssemblyDebugHelper : MonoBehaviour
{
    [Header("调试设置")]
    [SerializeField] private bool enableDebug = true;
    [SerializeField] private KeyCode debugKey = KeyCode.F1;
    
    [Header("组件引用")]
    [SerializeField] private AssemblyPartManager partManager;
    [SerializeField] private Neo4jAssemblyController assemblyController;
    
    void Start()
    {
        if (enableDebug)
        {
            // 自动查找组件
            if (partManager == null)
                partManager = FindObjectOfType<AssemblyPartManager>();
            
            if (assemblyController == null)
                assemblyController = FindObjectOfType<Neo4jAssemblyController>();
            
            Debug.Log("🔍 装配调试助手已启动，按 F1 键查看调试信息");
        }
    }
    
    void Update()
    {
        if (enableDebug && Input.GetKeyDown(debugKey))
        {
            ShowDebugInfo();
        }
    }
    
    /// <summary>
    /// 显示调试信息
    /// </summary>
    [ContextMenu("显示调试信息")]
    public void ShowDebugInfo()
    {
        Debug.Log("=== 装配系统调试信息 ===");
        
        // 检查零件管理器
        CheckPartManager();
        
        // 检查装配控制器
        CheckAssemblyController();
        
        // 检查场景中的零件
        CheckSceneParts();

        // 检查紧固件
        CheckFasteners();

        // 检查紧固件使用状态
        CheckFastenerUsageState();

        // 检查UI文本框
        CheckUIText();
        
        Debug.Log("=== 调试信息结束 ===");
    }
    
    /// <summary>
    /// 检查零件管理器状态
    /// </summary>
    private void CheckPartManager()
    {
        Debug.Log("📦 零件管理器状态:");
        
        if (partManager == null)
        {
            Debug.LogError("❌ 零件管理器未找到！");
            return;
        }
        
        Debug.Log($"   ✅ 零件管理器已找到: {partManager.gameObject.name}");
        Debug.Log($"   📊 状态信息: {partManager.GetStatusInfo()}");
        
        var visibleParts = partManager.GetVisibleParts();
        Debug.Log($"   👁️ 当前可见零件数: {visibleParts.Count}");
        
        if (visibleParts.Count > 0)
        {
            Debug.Log($"   📋 可见零件列表: {string.Join(", ", visibleParts)}");
        }
    }
    
    /// <summary>
    /// 检查装配控制器状态
    /// </summary>
    private void CheckAssemblyController()
    {
        Debug.Log("🎮 装配控制器状态:");
        
        if (assemblyController == null)
        {
            Debug.LogError("❌ 装配控制器未找到！");
            return;
        }
        
        Debug.Log($"   ✅ 装配控制器已找到: {assemblyController.gameObject.name}");
        
        // 使用反射获取私有字段信息
        var partNameToComponentField = typeof(Neo4jAssemblyController).GetField("partNameToComponent", 
            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        
        if (partNameToComponentField != null)
        {
            var partMapping = partNameToComponentField.GetValue(assemblyController) as System.Collections.IDictionary;
            if (partMapping != null)
            {
                Debug.Log($"   📋 装配控制器中的零件映射数: {partMapping.Count}");
                
                var keys = new List<string>();
                foreach (var key in partMapping.Keys)
                {
                    keys.Add(key.ToString());
                }
                
                if (keys.Count > 0)
                {
                    Debug.Log($"   📝 映射的零件名称: {string.Join(", ", keys)}");
                }
            }
        }
    }
    
    /// <summary>
    /// 检查场景中的零件
    /// </summary>
    private void CheckSceneParts()
    {
        Debug.Log("🏗️ 场景零件状态:");
        
        AssemblyPart[] allParts = FindObjectsOfType<AssemblyPart>(true); // 包括非激活的
        Debug.Log($"   📊 场景中零件总数: {allParts.Length}");
        
        int activeParts = 0;
        int inactiveParts = 0;
        
        foreach (AssemblyPart part in allParts)
        {
            if (part.gameObject.activeInHierarchy)
            {
                activeParts++;
                Debug.Log($"   ✅ 激活零件: {part.PartName} (GameObject: {part.gameObject.name})");
            }
            else
            {
                inactiveParts++;
                Debug.Log($"   ❌ 非激活零件: {part.PartName} (GameObject: {part.gameObject.name})");
            }
        }
        
        Debug.Log($"   📈 激活零件数: {activeParts}, 非激活零件数: {inactiveParts}");
    }

    /// <summary>
    /// 检查紧固件状态
    /// </summary>
    private void CheckFasteners()
    {
        Debug.Log("🔩 紧固件状态:");

        // 检查螺丝容器
        Transform screwsContainer = GameObject.Find("Screws")?.transform;
        if (screwsContainer != null)
        {
            int activeScrews = 0;
            int inactiveScrews = 0;

            foreach (Transform child in screwsContainer)
            {
                if (child.gameObject.activeInHierarchy)
                {
                    activeScrews++;
                    Debug.Log($"   ✅ 激活螺丝: {child.name}");
                }
                else
                {
                    inactiveScrews++;
                    Debug.Log($"   ❌ 非激活螺丝: {child.name}");
                }
            }

            Debug.Log($"   🔩 螺丝统计: 激活 {activeScrews}, 非激活 {inactiveScrews}");
        }
        else
        {
            Debug.LogWarning("   ⚠️ 未找到螺丝容器");
        }

        // 检查螺母容器
        Transform nutsContainer = GameObject.Find("Nuts")?.transform;
        if (nutsContainer != null)
        {
            int activeNuts = 0;
            int inactiveNuts = 0;

            foreach (Transform child in nutsContainer)
            {
                if (child.gameObject.activeInHierarchy)
                {
                    activeNuts++;
                    Debug.Log($"   ✅ 激活螺母: {child.name}");
                }
                else
                {
                    inactiveNuts++;
                    Debug.Log($"   ❌ 非激活螺母: {child.name}");
                }
            }

            Debug.Log($"   🔧 螺母统计: 激活 {activeNuts}, 非激活 {inactiveNuts}");
        }
        else
        {
            Debug.LogWarning("   ⚠️ 未找到螺母容器");
        }
    }

    /// <summary>
    /// 检查紧固件使用状态
    /// </summary>
    private void CheckFastenerUsageState()
    {
        Debug.Log("🔍 紧固件使用状态检查:");

        // 查找Neo4jAssemblyController
        Neo4jAssemblyController controller = FindObjectOfType<Neo4jAssemblyController>();
        if (controller != null)
        {
            // 使用反射访问私有字段usedFasteners
            var usedFastenersField = typeof(Neo4jAssemblyController).GetField("usedFasteners",
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

            if (usedFastenersField != null)
            {
                var usedFasteners = usedFastenersField.GetValue(controller) as System.Collections.Generic.HashSet<Transform>;
                if (usedFasteners != null)
                {
                    Debug.Log($"   📊 已使用紧固件数量: {usedFasteners.Count}");

                    foreach (Transform usedFastener in usedFasteners)
                    {
                        if (usedFastener != null)
                        {
                            Debug.Log($"   ✅ 已使用: {usedFastener.name}");
                        }
                    }
                }
                else
                {
                    Debug.LogWarning("   ⚠️ 无法获取已使用紧固件集合");
                }
            }
            else
            {
                Debug.LogWarning("   ⚠️ 无法访问usedFasteners字段");
            }
        }
        else
        {
            Debug.LogWarning("   ⚠️ 未找到Neo4jAssemblyController");
        }

        // 查找FastenerUsageTracker
        FastenerUsageTracker tracker = FindObjectOfType<FastenerUsageTracker>();
        if (tracker != null)
        {
            Debug.Log("   📈 调用FastenerUsageTracker报告...");
            tracker.ShowFastenerUsageReport();
        }
        else
        {
            Debug.Log("   ℹ️ 未找到FastenerUsageTracker组件");
        }
    }

    /// <summary>
    /// 检查UI文本框状态
    /// </summary>
    private void CheckUIText()
    {
        Debug.Log("📝 UI文本框状态:");
        
        Text[] allTexts = FindObjectsOfType<Text>();
        Debug.Log($"   📊 场景中文本框总数: {allTexts.Length}");
        
        foreach (Text text in allTexts)
        {
            Debug.Log($"   📄 文本框: {text.gameObject.name}");
            Debug.Log($"      激活状态: {text.gameObject.activeInHierarchy}");
            Debug.Log($"      启用状态: {text.enabled}");
            Debug.Log($"      当前文本: \"{text.text}\"");
            Debug.Log($"      字体: {(text.font != null ? text.font.name : "null")}");
            Debug.Log($"      字体大小: {text.fontSize}");
            Debug.Log($"      颜色: {text.color}");
        }
        
        // 检查零件管理器的文本框引用
        if (partManager != null)
        {
            var stepInfoTextField = typeof(AssemblyPartManager).GetField("stepInfoText", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            
            if (stepInfoTextField != null)
            {
                var stepInfoText = stepInfoTextField.GetValue(partManager) as Text;
                if (stepInfoText != null)
                {
                    Debug.Log($"   ✅ 零件管理器文本框已设置: {stepInfoText.gameObject.name}");
                    Debug.Log($"      当前显示文本: \"{stepInfoText.text}\"");
                }
                else
                {
                    Debug.LogWarning("   ⚠️ 零件管理器文本框未设置！");
                }
            }
        }
    }
    
    /// <summary>
    /// 测试零件显示
    /// </summary>
    [ContextMenu("测试零件显示")]
    public void TestPartDisplay()
    {
        if (partManager == null)
        {
            Debug.LogError("❌ 零件管理器未找到！");
            return;
        }
        
        Debug.Log("🧪 开始测试零件显示...");
        
        // 测试显示一个零件
        AssemblyPart[] allParts = FindObjectsOfType<AssemblyPart>(true);
        if (allParts.Length > 0)
        {
            string testPartName = allParts[0].PartName;
            Debug.Log($"🔧 测试显示零件: {testPartName}");
            partManager.ShowPart(testPartName);
            
            // 等待一帧后检查结果
            StartCoroutine(CheckPartDisplayResult(testPartName));
        }
        else
        {
            Debug.LogWarning("⚠️ 场景中没有找到零件！");
        }
    }
    
    /// <summary>
    /// 检查零件显示结果
    /// </summary>
    private System.Collections.IEnumerator CheckPartDisplayResult(string partName)
    {
        yield return new WaitForEndOfFrame();
        
        AssemblyPart[] allParts = FindObjectsOfType<AssemblyPart>();
        AssemblyPart targetPart = allParts.FirstOrDefault(p => p.PartName == partName);
        
        if (targetPart != null)
        {
            bool isActive = targetPart.gameObject.activeInHierarchy;
            Debug.Log($"🔍 零件 {partName} 显示结果: {(isActive ? "✅ 成功显示" : "❌ 仍然隐藏")}");
        }
        else
        {
            Debug.LogWarning($"⚠️ 未找到零件: {partName}");
        }
    }
    
    /// <summary>
    /// 测试装配步骤
    /// </summary>
    [ContextMenu("测试装配步骤")]
    public void TestAssemblyStep()
    {
        if (partManager == null)
        {
            Debug.LogError("❌ 零件管理器未找到！");
            return;
        }
        
        Debug.Log("🧪 开始测试装配步骤...");
        
        // 使用测试数据
        partManager.ProcessAssemblyStep(
            "安装连接块反1", 
            "中下段", 
            "SCREW", 
            "M6螺丝", 
            "M6螺母"
        );
        
        Debug.Log("✅ 装配步骤测试完成");
    }
}
