using UnityEngine;
using Woz.Logging; // 1. 引入日志系统

/// <summary>
/// 网络模块的顶层协调器，负责初始化和管理WebSocket与实验管理器。
/// 采用持久化单例模式，确保在场景切换时实例不被销毁。
/// </summary>
public class network_manager : MonoBehaviour
{
    // --- 单例模式实现 ---
    public static network_manager Instance { get; private set; }

    [Header("核心组件引用")]
    [SerializeField] private WebSocketManager webSocketManager;
    [SerializeField] private ExperimentManager experimentManager;

    [Header("调试设置")]
    [SerializeField] private bool enableDebugKeys = true;

    // 日志源标识
    private const string LOG_SOURCE = "NetworkManager";

    private void Awake()
    {
        // 实现持久化单例模式
        if (Instance == null)
        {
            Instance = this;
            DontDestroyOnLoad(gameObject); // 让此GameObject在加载新场景时不被销毁
            LogManager.Info(LOG_SOURCE, "网络管理器单例已初始化并标记为持久化。");
        }
        else if (Instance != this)
        {
            LogManager.Warning(LOG_SOURCE, "检测到重复的网络管理器实例，将销毁新的实例。");
            Destroy(gameObject); // 销毁重复的实例
            return;
        }
    }

    void Start()
    {
        InitializeNetworkComponents();
    }

    void Update()
    {
        // 仅在编辑器中处理调试输入
#if UNITY_EDITOR
        if (enableDebugKeys)
        {
            HandleDebugInput();
        }
#endif
    }

    private void InitializeNetworkComponents()
    {
        LogManager.Info(LOG_SOURCE, "开始初始化网络组件...");

        if (webSocketManager == null) webSocketManager = FindObjectOfType<WebSocketManager>();
        if (experimentManager == null) experimentManager = FindObjectOfType<ExperimentManager>();

        if (webSocketManager == null) LogManager.Warning(LOG_SOURCE, "未找到WebSocketManager组件！");
        else LogManager.Info(LOG_SOURCE, "WebSocketManager组件已连接");

        if (experimentManager == null) LogManager.Warning(LOG_SOURCE, "未找到ExperimentManager组件！");
        else LogManager.Info(LOG_SOURCE, "ExperimentManager组件已连接");

        LogManager.Info(LOG_SOURCE, "网络组件初始化完成");
    }

    /// <summary>
    /// 处理调试输入
    /// </summary>
    private void HandleDebugInput()
    {
        // F1: 显示网络状态
        if (Input.GetKeyDown(KeyCode.F1)) ShowNetworkStatus();

        // F2: 测试连接 (发送一个简单的状态更新)
        if (Input.GetKeyDown(KeyCode.F2)) TestNetworkConnection();

        // F5: 手动重连
        if (Input.GetKeyDown(KeyCode.F5)) ManualReconnect();
    }

    /// <summary>
    /// 显示网络状态信息
    /// </summary>
    [ContextMenu("显示网络状态")]
    public void ShowNetworkStatus()
    {
        LogManager.Info(LOG_SOURCE, "=== 网络状态报告 ===");

        if (webSocketManager != null)
        {
            LogManager.Info(LOG_SOURCE, $"连接状态: {webSocketManager.GetConnectionStatus()}");
        }
        else
        {
            LogManager.Warning(LOG_SOURCE, "WebSocket管理器: 未找到");
        }

        if (experimentManager == null)
        {
            LogManager.Warning(LOG_SOURCE, "实验管理器: 未找到");
        }

        LogManager.Info(LOG_SOURCE, "=== 网络状态报告结束 ===");
    }

    /// <summary>
    /// 测试网络连接（通过发送状态更新）
    /// </summary>
    [ContextMenu("测试网络连接")]
    public void TestNetworkConnection()
    {
        LogManager.Info(LOG_SOURCE, "正在测试网络连接 (发送状态更新)...");
        SendStatusUpdate();
    }

    /// <summary>
    /// 手动触发重连
    /// </summary>
    [ContextMenu("手动重连WebSocket")]
    public void ManualReconnect()
    {
        webSocketManager?.ManualReconnect();
    }

    // --- 公共API (作为其他模块的统一入口) ---

    /// <summary>
    /// 公共API：发送状态更新
    /// </summary>
    public void SendStatusUpdate()
    {
        webSocketManager?.SendStatusUpdate();
    }

    /// <summary>
    /// 公共API：发送实验数据
    /// </summary>
    public void SendExperimentData(string data)
    {
        webSocketManager?.SendExperimentData(data);
    }

    /// <summary>
    /// 公共API：记录实验事件
    /// </summary>
    public void RecordExperimentEvent(string eventType, int stepIndex = -1)
    {
        if (experimentManager != null)
        {
            switch (eventType.ToLower())
            {
                case "step_started":
                    if (stepIndex >= 0) experimentManager.OnStepStarted(stepIndex);
                    break;
                case "step_completed":
                    if (stepIndex >= 0) experimentManager.OnStepCompleted(stepIndex, true);
                    break;
                case "step_failed":
                    if (stepIndex >= 0) experimentManager.OnStepCompleted(stepIndex, false);
                    break;
            }
        }
    }

    void OnDestroy()
    {
        LogManager.Info(LOG_SOURCE, "网络管理器正在关闭...");
    }
}
