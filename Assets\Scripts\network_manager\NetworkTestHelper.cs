using UnityEngine;
using UnityEngine.UI;

/// <summary>
/// 网络测试助手
/// 用于验证网络模块的数据接收和处理功能
/// </summary>
public class NetworkTestHelper : MonoBehaviour
{
    [Header("网络组件引用")]
    [SerializeField] private WebSocketManager webSocketManager;
    [SerializeField] private network_manager networkManager;
    
    [Header("测试按钮")]
    [SerializeField] private Button testPlayCommandButton;
    [SerializeField] private Button testStepCommandButton;
    [SerializeField] private Button testSeeThroughButton;
    [SerializeField] private Button testStatusButton;
    
    [Header("测试配置")]
    [SerializeField] private bool enableKeyboardShortcuts = true;
    
    void Start()
    {
        InitializeTestHelper();
        SetupTestButtons();
    }
    
    void Update()
    {
        if (enableKeyboardShortcuts)
        {
            HandleTestKeyboard();
        }
    }
    
    /// <summary>
    /// 初始化测试助手
    /// </summary>
    private void InitializeTestHelper()
    {
        Debug.Log("【测试助手】网络测试助手初始化");
        
        // 自动查找组件
        if (webSocketManager == null)
        {
            webSocketManager = FindObjectOfType<WebSocketManager>();
        }
        
        if (networkManager == null)
        {
            networkManager = FindObjectOfType<network_manager>();
        }
        
        // 验证组件
        if (webSocketManager == null)
        {
            Debug.LogWarning("【测试助手】未找到WebSocketManager组件");
        }
        
        if (networkManager == null)
        {
            Debug.LogWarning("【测试助手】未找到network_manager组件");
        }
    }
    
    /// <summary>
    /// 设置测试按钮
    /// </summary>
    private void SetupTestButtons()
    {
        if (testPlayCommandButton != null)
        {
            testPlayCommandButton.onClick.AddListener(TestPlayCommand);
        }
        
        if (testStepCommandButton != null)
        {
            testStepCommandButton.onClick.AddListener(TestStepCommand);
        }
        
        if (testSeeThroughButton != null)
        {
            testSeeThroughButton.onClick.AddListener(TestSeeThroughCommand);
        }
        
        if (testStatusButton != null)
        {
            testStatusButton.onClick.AddListener(TestStatusUpdate);
        }
    }
    
    /// <summary>
    /// 处理测试键盘输入
    /// </summary>
    private void HandleTestKeyboard()
    {
        // T1: 测试播放命令
        if (Input.GetKeyDown(KeyCode.Alpha1))
        {
            TestPlayCommand();
        }
        
        // T2: 测试装配步骤命令
        if (Input.GetKeyDown(KeyCode.Alpha2))
        {
            TestStepCommand();
        }
        
        // T3: 测试透视命令
        if (Input.GetKeyDown(KeyCode.Alpha3))
        {
            TestSeeThroughCommand();
        }
        
        // T4: 测试状态更新
        if (Input.GetKeyDown(KeyCode.Alpha4))
        {
            TestStatusUpdate();
        }
        
        // T5: 测试所有命令
        if (Input.GetKeyDown(KeyCode.Alpha5))
        {
            TestAllCommands();
        }
    }
    
    /// <summary>
    /// 测试播放动画命令
    /// </summary>
    [ContextMenu("测试播放命令")]
    public void TestPlayCommand()
    {
        Debug.Log("【测试助手】模拟播放动画命令");
        
        // 模拟服务器发送的播放命令JSON
        string playCommandJson = @"{
            ""command"": ""play"",
            ""animation_name"": ""test_assembly_animation"",
            ""speed"": 1.5
        }";
        
        Debug.Log($"【测试助手】模拟收到服务器消息：{playCommandJson}");
        
        // 直接调用WebSocketManager的消息处理方法
        if (webSocketManager != null)
        {
            SimulateServerMessage(playCommandJson);
        }
    }
    
    /// <summary>
    /// 测试装配步骤命令
    /// </summary>
    [ContextMenu("测试装配步骤命令")]
    public void TestStepCommand()
    {
        Debug.Log("【测试助手】模拟装配步骤命令");
        
        // 模拟服务器发送的装配步骤命令JSON
        string stepCommandJson = @"{
            ""command"": ""execute_step"",
            ""step_data"": {
                ""movingPartName"": ""测试零件A"",
                ""targetPartName"": ""测试零件B"",
                ""movingPartRefPoint"": ""P1"",
                ""targetPartRefPoint"": ""P2"",
                ""connectionType"": ""SCREW"",
                ""fastenerType"": ""SCREW_NUT"",
                ""screwType"": ""M2X6"",
                ""nutType"": ""nut"",
                ""stepIndex"": 0,
                ""totalSteps"": 5,
                ""status"": ""in_progress""
            }
        }";
        
        Debug.Log($"【测试助手】模拟收到服务器消息：{stepCommandJson}");
        
        if (webSocketManager != null)
        {
            SimulateServerMessage(stepCommandJson);
        }
    }
    
    /// <summary>
    /// 测试透视功能命令
    /// </summary>
    [ContextMenu("测试透视命令")]
    public void TestSeeThroughCommand()
    {
        Debug.Log("【测试助手】模拟透视切换命令");
        
        // 随机选择开启或关闭
        string state = Random.value > 0.5f ? "on" : "off";
        
        string seeThroughCommandJson = $@"{{
            ""command"": ""seethrough"",
            ""state"": ""{state}""
        }}";
        
        Debug.Log($"【测试助手】模拟收到服务器消息：{seeThroughCommandJson}");
        
        if (webSocketManager != null)
        {
            SimulateServerMessage(seeThroughCommandJson);
        }
    }
    
    /// <summary>
    /// 测试状态更新
    /// </summary>
    [ContextMenu("测试状态更新")]
    public void TestStatusUpdate()
    {
        Debug.Log("【测试助手】测试状态更新发送");
        
        if (networkManager != null)
        {
            networkManager.SendStatusUpdate();
        }
        else if (webSocketManager != null)
        {
            webSocketManager.SendStatusUpdate();
        }
        else
        {
            Debug.LogWarning("【测试助手】无法发送状态更新：未找到网络管理器");
        }
    }
    
    /// <summary>
    /// 测试所有命令
    /// </summary>
    [ContextMenu("测试所有命令")]
    public void TestAllCommands()
    {
        Debug.Log("【测试助手】开始测试所有命令类型");
        
        StartCoroutine(TestAllCommandsSequence());
    }
    
    /// <summary>
    /// 依次测试所有命令的协程
    /// </summary>
    private System.Collections.IEnumerator TestAllCommandsSequence()
    {
        Debug.Log("【测试助手】=== 开始完整命令测试序列 ===");
        
        // 1. 测试场景加载命令
        TestSceneLoadCommand();
        yield return new WaitForSeconds(1f);
        
        // 2. 测试播放命令
        TestPlayCommand();
        yield return new WaitForSeconds(1f);
        
        // 3. 测试装配步骤命令
        TestStepCommand();
        yield return new WaitForSeconds(1f);
        
        // 4. 测试透视命令
        TestSeeThroughCommand();
        yield return new WaitForSeconds(1f);
        
        // 5. 测试失败反馈命令
        TestFailureFeedbackCommand();
        yield return new WaitForSeconds(1f);
        
        // 6. 测试完成命令
        TestCompletionCommand();
        yield return new WaitForSeconds(1f);
        
        // 7. 测试状态更新
        TestStatusUpdate();
        
        Debug.Log("【测试助手】=== 完整命令测试序列结束 ===");
    }
    
    /// <summary>
    /// 测试场景加载命令
    /// </summary>
    private void TestSceneLoadCommand()
    {
        string loadSceneJson = @"{
            ""command"": ""load_scene"",
            ""scene_id"": ""assembly_scene_001""
        }";
        
        Debug.Log($"【测试助手】模拟收到服务器消息：{loadSceneJson}");
        SimulateServerMessage(loadSceneJson);
    }
    
    /// <summary>
    /// 测试失败反馈命令
    /// </summary>
    private void TestFailureFeedbackCommand()
    {
        string failureJson = @"{
            ""command"": ""show_failure_feedback"",
            ""message"": ""装配步骤执行失败，请检查零件位置""
        }";
        
        Debug.Log($"【测试助手】模拟收到服务器消息：{failureJson}");
        SimulateServerMessage(failureJson);
    }
    
    /// <summary>
    /// 测试完成命令
    /// </summary>
    private void TestCompletionCommand()
    {
        string completionJson = @"{
            ""command"": ""sequence_completed""
        }";
        
        Debug.Log($"【测试助手】模拟收到服务器消息：{completionJson}");
        SimulateServerMessage(completionJson);
    }
    
    /// <summary>
    /// 模拟服务器消息
    /// </summary>
    private void SimulateServerMessage(string jsonMessage)
    {
        if (webSocketManager != null)
        {
            // 使用公共方法调用命令处理
            webSocketManager.ProcessServerCommandPublic(jsonMessage);
        }
        else
        {
            Debug.LogWarning("【测试助手】无法模拟服务器消息：WebSocketManager未找到");
        }
    }
    
    /// <summary>
    /// 显示测试帮助信息
    /// </summary>
    [ContextMenu("显示测试帮助")]
    public void ShowTestHelp()
    {
        Debug.Log("=== 【测试助手】测试快捷键说明 ===");
        Debug.Log("1键: 测试播放动画命令");
        Debug.Log("2键: 测试装配步骤命令");
        Debug.Log("3键: 测试透视切换命令");
        Debug.Log("4键: 测试状态更新");
        Debug.Log("5键: 测试所有命令");
        Debug.Log("F1键: 显示网络状态（network_manager）");
        Debug.Log("F2键: 测试网络连接（network_manager）");
        Debug.Log("F3键: 开始实验会话（network_manager）");
        Debug.Log("F4键: 结束实验会话（network_manager）");
        Debug.Log("=== 测试帮助结束 ===");
    }
} 