using UnityEngine;
using System.Collections;

public class AssemblyAnimation : MonoBehaviour
{
    public Transform longU; // 长U型架（根物体）
    public Transform shortU; // 小U型架（根物体）
    public Transform screw; // 螺丝（根物体）
    public Transform nut; // 螺母（根物体）
    public Transform longUHole; // 长U的孔位参考点（子物体）
    public Transform shortUHole; // 小U的孔位参考点（子物体）
    public Transform screwMountPoint; // 螺丝的安装点（子物体）
    public Transform nutMountPoint; // 螺母的安装点（子物体）

    public float screwLength = 0.005f; // 螺丝螺杆长度（米，缩放后调整）
    public float nutThickness = 0.001f; // 螺母厚度（米，缩放后调整）
    public float animationDuration = 2f; // 每段动画持续时间
    public float initialOffset = 0.01f; // 废弃参数：螺丝初始延长线偏移（米）

    // 孔位方向轴
    public enum Axis { X, Y, Z }
    public Axis holeDirectionAxis = Axis.Z; // 孔位方向轴（默认Z轴）

    
    [Header("螺丝移动参数")]
    public float initialDistanceFromHole = 0.05f; // 螺丝在延长线上的初始位置到孔位的距离
    public float approachDistance = 0.02f; // 螺丝接近孔位的距离
    public bool useModifiedPath = true; // 是否使用优化的路径
    
    [Header("螺丝旋转和位置微调")]
    public float screwRotationDuration = 1.0f; // 螺丝旋转时间
    public float screwInitialElevation = 0.0f; // 螺丝初始位置的额外高度（Y轴）
    public float screwFinalElevation = 0.0f; // 螺丝最终位置的额外高度（Y轴）

    void Start()
    {
        // 验证绑定
        if (!longUHole || !shortUHole || !screw || !nut || !screwMountPoint || !nutMountPoint)
        {
            Debug.LogError("存在未绑定的物体！");
            return;
        }
        StartCoroutine(AssembleAnimation());
    }

    IEnumerator AssembleAnimation()
    {
        // 验证缩放
        if (screw.localScale != Vector3.one || screwMountPoint.localScale != Vector3.one)
        {
            Debug.LogWarning($"螺丝或screwMountPoint缩放非(1,1,1)，可能影响位置计算！" +
                $"screw.scale: {screw.localScale}, screwMountPoint.scale: {screwMountPoint.localScale}");
        }

        // 恢复原来的U型架对齐方式
        Debug.Log("开始U型架对齐 ========================");
        
        // 原始的计算方法
        Vector3 localHolePos = shortU.InverseTransformPoint(shortUHole.position);
        Vector3 targetPos = longUHole.position - shortU.TransformPoint(localHolePos) + shortU.position;
        Quaternion targetRot = CalculateTargetRotation();
        
        Debug.Log($"长U孔位置: {longUHole.position}, 小U孔位置: {shortUHole.position}");
        Debug.Log($"小U目标位置: {targetPos}, 目标旋转: {targetRot.eulerAngles}");
        
        // 使用原始的MoveAndRotate方法
        yield return StartCoroutine(MoveAndRotate(shortU, targetPos, targetRot, animationDuration, false));

        // 验证孔位对齐
        float holeDistance = Vector3.Distance(shortUHole.position, longUHole.position);
        float holeAngle = Quaternion.Angle(shortUHole.rotation, longUHole.rotation);
        
        Debug.Log($"对齐后孔位距离: {holeDistance}, 旋转角度差: {holeAngle}°");
        
        if (holeDistance > 0.001f || holeAngle > 0.5f)
        {
            Debug.LogWarning("孔位未完全对齐！");
            Debug.Log($"位置差: {Vector3.Distance(shortUHole.position, longUHole.position)}, " +
                      $"旋转差: {Quaternion.Angle(shortUHole.rotation, longUHole.rotation)}");
        }
        
        Debug.Log("U型架对齐完成 ========================");

        // 孔位方向
        Vector3 holeDirection = GetAxisDirection(longUHole);
        Debug.Log($"孔位位置: {longUHole.position}, 孔位方向: {holeDirection}");

        if (useModifiedPath)
        {
            // 修改后的螺丝移动路径
            yield return StartCoroutine(MoveScrewImproved(holeDirection));
        }
        else
        {
            // 原始的螺丝移动路径
            // 步骤2：移动螺丝到孔位Z轴延长线
            Vector3 screwLinePos = longUHole.position - holeDirection * initialOffset;
            Quaternion screwLineRot = Quaternion.LookRotation(-holeDirection, longUHole.up);
            Debug.Log($"螺丝延长线目标位置 (screwMountPoint): {screwLinePos}, 目标旋转: {screwLineRot}");
            yield return StartCoroutine(MoveAndRotate(screw, screwLinePos, screwLineRot, animationDuration, true));
            Debug.Log($"螺丝延长线实际位置 (screwMountPoint): {screwMountPoint.position}, 螺丝位置: {screw.position}");

            // 步骤3：平移螺丝到孔位
            Vector3 screwTargetPos = longUHole.position;
            Debug.Log($"螺丝孔位目标位置 (screwMountPoint): {screwTargetPos}, 保持旋转: {screwLineRot}");
            yield return StartCoroutine(MoveAndRotate(screw, screwTargetPos, screwLineRot, animationDuration, true));
            Debug.Log($"螺丝孔位实际位置 (screwMountPoint): {screwMountPoint.position}, 螺丝位置: {screw.position}");

            // 步骤4：旋转螺丝精确对齐
            Quaternion screwFinalRot = Quaternion.LookRotation(-holeDirection, longUHole.up);
            Debug.Log($"螺丝最终旋转: {screwFinalRot}");
            yield return StartCoroutine(MoveAndRotate(screw, screwTargetPos, screwFinalRot, animationDuration / 2, true));
            Debug.Log($"螺丝旋转后实际位置 (screwMountPoint): {screwMountPoint.position}, 螺丝位置: {screw.position}");

            // 验证螺丝位置
            if (Vector3.Distance(screwMountPoint.position, longUHole.position) > 0.001f)
            {
                Debug.LogWarning($"螺丝安装点未对齐！" +
                    $"screwMountPoint.position: {screwMountPoint.position}, longUHole.position: {longUHole.position}");
            }

            // 步骤5：插入螺丝
            Vector3 screwInsertPos = screw.position + holeDirection * screwLength;
            Debug.Log($"螺丝插入目标位置: {screwInsertPos}");
            yield return StartCoroutine(Move(screw, screwInsertPos, animationDuration));
            Debug.Log($"螺丝插入实际位置 (screwMountPoint): {screwMountPoint.position}, 螺丝位置: {screw.position}");
        }

        // 改进螺母动画
        yield return StartCoroutine(MoveNutImproved(holeDirection));
        
        Debug.Log("组装动画全部完成！");
    }

    // 计算shortU的目标旋转
    Quaternion CalculateTargetRotation()
    {
        Quaternion currentHoleRot = shortUHole.rotation;
        Quaternion targetHoleRot = longUHole.rotation;
        Quaternion deltaRot = targetHoleRot * Quaternion.Inverse(currentHoleRot);
        return deltaRot * shortU.rotation;
    }

    // 获取指定轴的方向
    Vector3 GetAxisDirection(Transform transform)
    {
        Vector3 direction;
        switch (holeDirectionAxis)
        {
            case Axis.X: direction = transform.right; break;
            case Axis.Y: direction = transform.up; break;
            case Axis.Z: direction = transform.forward; break;
            default: direction = transform.forward; break;
        }
        
        // 确保方向向量是单位向量
        if (direction.magnitude > 0)
        {
            direction.Normalize();
        }
        else
        {
            Debug.LogWarning($"轴向为零向量! transform: {transform.name}");
            // 使用世界坐标系中的Z轴作为默认方向
            direction = Vector3.forward;
        }
        
        return direction;
    }

    // 移动和旋转协程 
    IEnumerator MoveAndRotate(Transform target, Vector3 targetPos, Quaternion targetRot, float duration, bool isMountPoint)
    {
        float elapsed = 0f;
        Vector3 startPos = target.position;
        Quaternion startRot = target.rotation;

        // 确定对应的MountPoint
        Transform mountPoint = null;
        if (target == screw && screwMountPoint != null)
        {
            mountPoint = screwMountPoint;
        }
        else if (target == nut && nutMountPoint != null)
        {
            mountPoint = nutMountPoint;
        }

        // 如果是MountPoint，调整目标位置
        Vector3 adjustedTargetPos = targetPos;
        if (isMountPoint && mountPoint != null)
        {
            // 记录初始时的本地偏移
            Vector3 initialLocalOffset = target.InverseTransformPoint(mountPoint.position);
            
            // 存储世界空间偏移
            Vector3 worldOffset = mountPoint.position - target.position;
            
            // 计算调整后的目标位置（让物体移动，而非挂载点直接移动到目标）
            adjustedTargetPos = targetPos - worldOffset;
            
            Debug.Log($"MoveAndRotate - 目标: {target.name}, 挂载点: {mountPoint.name}");
            Debug.Log($"  初始目标位置: {targetPos}, 调整后位置: {adjustedTargetPos}");
            Debug.Log($"  世界空间偏移: {worldOffset}, 本地偏移: {initialLocalOffset}");
        }

        // 计算移动方向向量
        Vector3 moveDirection = adjustedTargetPos - startPos;
        float moveDist = moveDirection.magnitude;

        while (elapsed < duration)
        {
            elapsed += Time.deltaTime;
            float t = Mathf.SmoothStep(0f, 1f, elapsed / duration);
            
            // 位置插值
            target.position = Vector3.Lerp(startPos, adjustedTargetPos, t);
            
            // 旋转插值
            target.rotation = Quaternion.Slerp(startRot, targetRot, t);
            
            // 如果有挂载点，确保其位置和旋转跟随主物体
            if (mountPoint != null)
            {
                UpdateMountPointPosition(target, mountPoint);
            }
            
            yield return null;
        }

        // 确保最终位置和旋转准确
        target.position = adjustedTargetPos;
        target.rotation = targetRot;
        
        // 确保最后挂载点位置正确
        if (mountPoint != null)
        {
            UpdateMountPointPosition(target, mountPoint);
            Debug.Log($"MoveAndRotate完成 - 目标: {target.name}, 最终位置: {target.position}");
            Debug.Log($"  挂载点: {mountPoint.name}, 最终位置: {mountPoint.position}");
            Debug.Log($"  挂载点与目标位置距离: {Vector3.Distance(mountPoint.position, targetPos)}");
        }
    }

    // 新增：更新Mount点位置
    void UpdateMountPointPosition(Transform target, Transform mountPoint)
    {
        if (target == null || mountPoint == null) return;
        
        // 获取从父物体到挂载点的本地坐标偏移
        Vector3 localOffset = target.InverseTransformPoint(mountPoint.position);
        
        // 将挂载点位置设置为当前物体位置加上本地偏移的世界坐标转换
        mountPoint.position = target.TransformPoint(localOffset);
        
        // 同步旋转
        mountPoint.rotation = target.rotation;
        
        // 添加调试输出
        Debug.Log($"更新挂载点 - 物体: {target.name}, 挂载点: {mountPoint.name}");
        Debug.Log($"  物体位置: {target.position}, 挂载点位置: {mountPoint.position}");
        Debug.Log($"  本地偏移: {localOffset}");
    }
    
    // 更新Move方法以支持mountPoint
    IEnumerator Move(Transform target, Vector3 targetPos, float duration)
    {
        float elapsed = 0f;
        Vector3 startPos = target.position;
        
        // 确定是否有挂载点
        Transform mountPoint = null;
        if (target == screw && screwMountPoint != null)
        {
            mountPoint = screwMountPoint;
        }
        else if (target == nut && nutMountPoint != null)
        {
            mountPoint = nutMountPoint;
        }
        
        // 记录挂载点的世界空间偏移
        Vector3 mountPointWorldOffset = Vector3.zero;
        if (mountPoint != null)
        {
            mountPointWorldOffset = mountPoint.position - target.position;
            Debug.Log($"Move开始 - 目标: {target.name}, 挂载点: {mountPoint.name}");
            Debug.Log($"  起始位置: {startPos}, 目标位置: {targetPos}");
            Debug.Log($"  世界空间偏移: {mountPointWorldOffset}");
        }

        while (elapsed < duration)
        {
            elapsed += Time.deltaTime;
            float t = Mathf.SmoothStep(0f, 1f, elapsed / duration);
            target.position = Vector3.Lerp(startPos, targetPos, t);
            
            // 如果有挂载点，确保其位置跟随主物体
            if (mountPoint != null)
            {
                UpdateMountPointPosition(target, mountPoint);
            }
            
            yield return null;
        }

        // 确保最终位置准确
        target.position = targetPos;
        
        // 确保最后挂载点位置正确
        if (mountPoint != null)
        {
            UpdateMountPointPosition(target, mountPoint);
            Debug.Log($"Move完成 - 目标: {target.name}, 最终位置: {target.position}");
            Debug.Log($"  挂载点: {mountPoint.name}, 最终位置: {mountPoint.position}");
            Debug.Log($"  最终偏移: {mountPoint.position - target.position}");
        }
    }

    // 修改RotateAndMoveNut方法以更新挂载点
    IEnumerator RotateAndMoveNut(Transform target, float angle, Vector3 targetPos, float duration)
    {
        float elapsed = 0f;
        Vector3 startPos = target.position;
        Quaternion startRot = target.rotation;
        Vector3 rotationAxis = GetAxisDirection(target);
        
        // 获取对应的挂载点
        Transform mountPoint = null;
        if (target == nut && nutMountPoint != null)
        {
            mountPoint = nutMountPoint;
        }

        // 记录挂载点的世界空间偏移
        Vector3 mountPointWorldOffset = Vector3.zero;
        if (mountPoint != null)
        {
            mountPointWorldOffset = mountPoint.position - target.position;
            Debug.Log($"RotateAndMoveNut开始 - 目标: {target.name}, 挂载点: {mountPoint.name}");
            Debug.Log($"  起始位置: {startPos}, 目标位置: {targetPos}");
            Debug.Log($"  世界空间偏移: {mountPointWorldOffset}, 旋转轴: {rotationAxis}");
        }

        // 计算最终位置，考虑到挂载点与物体的偏移
        Vector3 adjustedTargetPos = targetPos;
        if (mountPoint != null)
        {
            adjustedTargetPos = targetPos - mountPointWorldOffset;
            Debug.Log($"  调整后的目标位置: {adjustedTargetPos}");
        }

        while (elapsed < duration)
        {
            elapsed += Time.deltaTime;
            float t = Mathf.SmoothStep(0f, 1f, elapsed / duration);
            
            // 位置插值
            target.position = Vector3.Lerp(startPos, adjustedTargetPos, t);
            
            // 计算当前已旋转角度
            float currentAngle = angle * t;
            
            // 绕自身轴旋转
            Quaternion additionalRotation = Quaternion.AngleAxis(currentAngle, rotationAxis);
            target.rotation = startRot * additionalRotation;
            
            // 如果有挂载点，更新其位置
            if (mountPoint != null)
            {
                UpdateMountPointPosition(target, mountPoint);
            }
            
            yield return null;
        }

        // 确保最终位置和旋转准确
        target.position = adjustedTargetPos;
        
        // 最终旋转（总角度）
        Quaternion finalRotation = Quaternion.AngleAxis(angle, rotationAxis);
        target.rotation = startRot * finalRotation;
        
        // 确保最后挂载点位置正确
        if (mountPoint != null)
        {
            UpdateMountPointPosition(target, mountPoint);
            Debug.Log($"RotateAndMoveNut完成 - 目标: {target.name}, 最终位置: {target.position}");
            Debug.Log($"  挂载点: {mountPoint.name}, 最终位置: {mountPoint.position}");
            Debug.Log($"  与目标位置距离: {Vector3.Distance(mountPoint.position, targetPos)}");
        }
    }

    // 新增方法：绕特定轴心点旋转物体
    IEnumerator RotateAroundPivot(Transform target, Vector3 pivotPoint, Vector3 axis, float angle, float duration)
    {
        float elapsed = 0f;
        Quaternion startRot = target.rotation;
        Vector3 startPosRelativeToPivot = target.position - pivotPoint;

        while (elapsed < duration)
        {
            elapsed += Time.deltaTime;
            float t = Mathf.SmoothStep(0f, 1f, elapsed / duration);
            
            // 计算当前旋转角度
            float currentAngle = angle * t;
            
            // 应用旋转
            Quaternion rotation = Quaternion.AngleAxis(currentAngle, axis);
            Vector3 newPosRelativeToPivot = rotation * startPosRelativeToPivot;
            
            // 设置新位置
            target.position = pivotPoint + newPosRelativeToPivot;
            
            // 同时旋转物体本身
            target.rotation = rotation * startRot;
            
            yield return null;
        }
    }

    // 调试：显示孔位、螺丝和螺母方向
    void OnDrawGizmos()
    {
        if (longUHole)
        {
            Gizmos.color = Color.blue;
            Vector3 holeDir = GetAxisDirection(longUHole);
            Gizmos.DrawRay(longUHole.position, holeDir * 0.1f);
            Gizmos.DrawWireSphere(longUHole.position, 0.01f);
            Gizmos.DrawLine(longUHole.position - holeDir * 0.2f, longUHole.position + holeDir * 0.2f);
        }
        if (shortUHole)
        {
            Gizmos.color = Color.green;
            Gizmos.DrawRay(shortUHole.position, GetAxisDirection(shortUHole) * 0.1f);
        }
        if (screwMountPoint)
        {
            Gizmos.color = Color.red;
            Gizmos.DrawRay(screwMountPoint.position, -screwMountPoint.forward * 0.1f);
            Gizmos.DrawWireSphere(screwMountPoint.position, 0.01f);
        }
        if (nutMountPoint)
        {
            Gizmos.color = Color.yellow;
            Gizmos.DrawRay(nutMountPoint.position, nutMountPoint.forward * 0.1f);
        }
    }

    // 新增：打印所有关键物体位置和旋转信息
    public void PrintDebugInfo()
    {
        Debug.Log("==== 调试信息 ====");
        if (longU) Debug.Log($"长U位置: {longU.position}, 旋转: {longU.rotation.eulerAngles}, 缩放: {longU.lossyScale}");
        if (shortU) Debug.Log($"小U位置: {shortU.position}, 旋转: {shortU.rotation.eulerAngles}, 缩放: {shortU.lossyScale}");
        if (screw) Debug.Log($"螺丝位置: {screw.position}, 旋转: {screw.rotation.eulerAngles}, 缩放: {screw.lossyScale}");
        if (nut) Debug.Log($"螺母位置: {nut.position}, 旋转: {nut.rotation.eulerAngles}, 缩放: {nut.lossyScale}");
        
        if (longUHole) Debug.Log($"长U孔位置: {longUHole.position}, 旋转: {longUHole.rotation.eulerAngles}");
        if (shortUHole) Debug.Log($"小U孔位置: {shortUHole.position}, 旋转: {shortUHole.rotation.eulerAngles}");
        if (screwMountPoint) Debug.Log($"螺丝挂载点位置: {screwMountPoint.position}, 旋转: {screwMountPoint.rotation.eulerAngles}");
        if (nutMountPoint) Debug.Log($"螺母挂载点位置: {nutMountPoint.position}, 旋转: {nutMountPoint.rotation.eulerAngles}");
        
        if (longUHole && shortUHole)
        {
            Debug.Log($"孔位距离: {Vector3.Distance(longUHole.position, shortUHole.position)}");
            Debug.Log($"孔位旋转角度差: {Quaternion.Angle(longUHole.rotation, shortUHole.rotation)}");
        }
        
        if (longUHole && screwMountPoint)
        {
            Debug.Log($"长U孔与螺丝挂载点距离: {Vector3.Distance(longUHole.position, screwMountPoint.position)}");
        }
        
        // 添加螺丝和螺母相关测量
        Vector3 holeDirection = GetAxisDirection(longUHole);
        Debug.Log($"孔位轴方向: {holeDirection}");
        
        if (longUHole && nutMountPoint)
        {
            // 计算螺丝尾部位置
            Vector3 screwEndPosition = longUHole.position + holeDirection * screwLength;
            Debug.Log($"理论螺丝尾部位置: {screwEndPosition}");
            Debug.Log($"螺母挂载点与螺丝尾部距离: {Vector3.Distance(nutMountPoint.position, screwEndPosition)}");
            
            // 计算螺母理论位置
            Vector3 nutTheoreticalPosition = screwEndPosition;
            Debug.Log($"螺母理论位置: {nutTheoreticalPosition}");
            Debug.Log($"螺母实际位置: {nut.position}, 差距: {Vector3.Distance(nut.position, nutTheoreticalPosition)}");
            
            // 检查螺母旋转是否与孔位方向一致
            Vector3 nutForward = -nutMountPoint.forward; // 假设螺母的前方是安装方向
            float angleWithHoleDirection = Vector3.Angle(nutForward, holeDirection);
            Debug.Log($"螺母方向与孔位方向夹角: {angleWithHoleDirection}°");
        }
    }

    void Update()
    {
        // 按下F1键打印调试信息
        if (Input.GetKeyDown(KeyCode.F1))
        {
            PrintDebugInfo();
        }
    }

    // 新增：改进的螺丝移动路径
    IEnumerator MoveScrewImproved(Vector3 holeDirection)
    {
        // 确保screwMountPoint存在
        if (screwMountPoint == null)
        {
            Debug.LogError("螺丝挂载点未设置！");
            yield break;
        }
        
        Debug.Log("开始螺丝动画 ========================");
        
        // 1. 计算各个关键位置
        // 孔位延长线上的初始点（添加可选的Y轴高度偏移）
        Vector3 initialPointForMountPoint = longUHole.position - holeDirection * initialDistanceFromHole + Vector3.up * screwInitialElevation;
        // 孔位位置（添加可选的Y轴高度偏移）
        Vector3 holePoint = longUHole.position + Vector3.up * screwFinalElevation;
        // 接近位置：在孔位附近但不完全对齐
        Vector3 approachPointForMountPoint = longUHole.position - holeDirection * approachDistance + Vector3.up * screwFinalElevation;
        
        Debug.Log($"孔位: {holePoint}, 延长线上点: {initialPointForMountPoint}, 接近点: {approachPointForMountPoint}");
        Debug.Log($"距离参数 - 初始距离: {initialDistanceFromHole}m, 接近距离: {approachDistance}m");
        
        // 2. 计算旋转角度
        // 初始旋转：大致朝向孔位
        Vector3 directionToHole = (holePoint - screw.position).normalized;
        Quaternion initialRotation = Quaternion.LookRotation(-directionToHole, Vector3.up);
        
        // 最终旋转：精确对齐孔位方向
        Quaternion finalRotation = Quaternion.LookRotation(-holeDirection, longUHole.up);
        
        Debug.Log($"初始旋转: {initialRotation.eulerAngles}, 最终旋转: {finalRotation.eulerAngles}");
        
        // 第一步：移动螺丝挂载点到延长线上的初始位置
        Debug.Log($"1. 螺丝移动到延长线初始位置: {initialPointForMountPoint}");
        yield return StartCoroutine(MoveAndRotateByMountPoint(screw, screwMountPoint, initialPointForMountPoint, initialRotation, animationDuration));
        
        // 新增步骤：在延长线上调整螺丝方向
        Debug.Log("2. 螺丝在延长线上调整方向");
        yield return StartCoroutine(RotateInPlace(screw, screwMountPoint, finalRotation, screwRotationDuration));
        
        // 第三步：移动螺丝挂载点到孔位附近（已经调整好方向）
        Debug.Log($"3. 螺丝平移到孔位附近: {approachPointForMountPoint}");
        yield return StartCoroutine(MoveByMountPoint(screw, screwMountPoint, approachPointForMountPoint, animationDuration * 0.7f));
        
        // 第四步：精确移动螺丝挂载点到孔位
        Debug.Log($"4. 螺丝精确移动到孔位: {holePoint}");
        yield return StartCoroutine(MoveByMountPoint(screw, screwMountPoint, holePoint, animationDuration * 0.5f));
        
        // 验证螺丝位置
        float alignmentDistance = Vector3.Distance(screwMountPoint.position, holePoint);
        Debug.Log($"螺丝挂载点与孔位对齐后的距离: {alignmentDistance}");
        
        if (alignmentDistance > 0.001f)
        {
            Debug.LogWarning($"螺丝安装点未对齐！");
            // 强制修正位置确保完全对齐
            AlignTransformPosition(screwMountPoint, holePoint);
        }
        
        // 第五步：插入螺丝 - 沿孔位方向移动
        Vector3 screwInsertedPosition = holePoint + holeDirection * screwLength;
        Debug.Log($"5. 螺丝插入到最终位置: {screwInsertedPosition}, 从: {holePoint}, 方向: {holeDirection}, 长度: {screwLength}");
        yield return StartCoroutine(MoveByMountPoint(screw, screwMountPoint, screwInsertedPosition, animationDuration));
        
        // 最终验证
        Debug.Log($"最终螺丝位置: {screw.position}, 挂载点位置: {screwMountPoint.position}");
        Debug.Log($"螺丝插入距离: {Vector3.Distance(holePoint, screwMountPoint.position)}");
        
        Debug.Log("螺丝动画完成 ========================");
    }

    // 新增：原地旋转物体，保持挂载点位置不变
    IEnumerator RotateInPlace(Transform target, Transform mountPoint, Quaternion targetRotation, float duration)
    {
        if (target == null || mountPoint == null)
        {
            Debug.LogError("目标或挂载点为空！");
            yield break;
        }
        
        // 记录当前位置和旋转
        Vector3 fixedPosition = mountPoint.position;
        Quaternion startRotation = target.rotation;
        
        // 计算初始偏移
        Vector3 initialOffset = target.position - mountPoint.position;
        
        // 旋转动画
        float elapsed = 0f;
        while (elapsed < duration)
        {
            elapsed += Time.deltaTime;
            float t = Mathf.SmoothStep(0f, 1f, elapsed / duration);
            
            // 1. 保持挂载点位置不变
            mountPoint.position = fixedPosition;
            
            // 2. 应用旋转插值
            Quaternion currentRotation = Quaternion.Slerp(startRotation, targetRotation, t);
            target.rotation = currentRotation;
            
            // 3. 计算旋转后的偏移
            Vector3 rotatedOffset = currentRotation * Quaternion.Inverse(startRotation) * initialOffset;
            
            // 4. 更新物体位置以维持挂载点位置不变
            target.position = mountPoint.position + rotatedOffset;
            
            yield return null;
        }
        
        // 确保最终旋转和位置准确
        target.rotation = targetRotation;
        mountPoint.position = fixedPosition;
        
        // 计算最终旋转后的偏移
        Vector3 finalOffset = targetRotation * Quaternion.Inverse(startRotation) * initialOffset;
        target.position = mountPoint.position + finalOffset;
        
        Debug.Log($"RotateInPlace完成 - 目标: {target.name}, 最终旋转: {target.rotation.eulerAngles}");
        Debug.Log($"  挂载点位置: {mountPoint.position}, 保持不变");
    }

    // 新增：改进的螺母移动方法
    IEnumerator MoveNutImproved(Vector3 holeDirection)
    {
        Debug.Log("开始螺母动画 ========================");
        
        // 确保nutMountPoint存在
        if (nutMountPoint == null)
        {
            Debug.LogError("螺母挂载点未设置！");
            yield break;
        }
        
        // 1. 计算螺丝尾部位置（螺母目标位置）
        Vector3 screwEndPosition = longUHole.position + holeDirection * screwLength;
        Debug.Log($"螺丝尾部位置: {screwEndPosition}");
        
        // 2. 计算螺母初始位置（在螺丝尾部旁边，稍微偏上）
        Vector3 nutStartPosition = screwEndPosition + Vector3.up * 0.03f + holeDirection * (initialDistanceFromHole * 0.3f);
        Debug.Log($"螺母初始位置: {nutStartPosition}");
        
        // 3. 计算螺母初始旋转（与螺丝轴向对齐）
        Quaternion nutStartRotation = Quaternion.LookRotation(holeDirection, Vector3.up);
        
        // 4. 移动螺母到初始位置 - 直接移动挂载点到目标位置，物体会自动跟随
        Debug.Log("螺母移动到初始位置");
        
        // 计算挂载点应该移动到的位置
        Vector3 mountPointStartPos = nutStartPosition;
        yield return StartCoroutine(MoveAndRotateByMountPoint(nut, nutMountPoint, mountPointStartPos, nutStartRotation, animationDuration));
        
        // 5. 移动螺母到螺丝尾部 - 直接移动挂载点到螺丝尾部
        Debug.Log("螺母移动到螺丝尾部");
        yield return StartCoroutine(MoveByMountPoint(nut, nutMountPoint, screwEndPosition, animationDuration * 0.7f));
        
        // 6. 拧紧螺母（旋转并向内移动一小段距离）
        float rotationAngle = 360f * 3; // 旋转3圈
        
        // 计算拧紧后的位置（沿着螺杆向内移动一点）
        Vector3 nutTightenedPosition = screwEndPosition - holeDirection * (nutThickness * 0.7f);
        Debug.Log($"螺母拧紧, 从: {screwEndPosition} 到: {nutTightenedPosition}, 旋转角度: {rotationAngle}°");
        
        // 7. 执行螺母拧紧动画 - 先旋转螺母，然后移动
        // 计算螺母旋转后的方向
        Quaternion currentRotation = nut.rotation;
        Quaternion targetRotation = currentRotation * Quaternion.AngleAxis(rotationAngle, holeDirection);
        
        // 7.1 先旋转螺母（原地）
        Debug.Log("螺母开始旋转");
        yield return StartCoroutine(RotateInPlace(nut, nutMountPoint, targetRotation, animationDuration * 0.6f));
        
        // 7.2 然后移动螺母到拧紧位置
        Debug.Log("螺母移动到拧紧位置");
        yield return StartCoroutine(MoveByMountPoint(nut, nutMountPoint, nutTightenedPosition, animationDuration * 0.4f));
        
        // 8. 验证最终位置
        float finalDistance = Vector3.Distance(nutMountPoint.position, nutTightenedPosition);
        Debug.Log($"螺母挂载点与理论位置最终距离: {finalDistance}");
        
        if (finalDistance > 0.002f)
        {
            Debug.LogWarning("螺母可能未正确对齐到最终位置!");
            // 尝试强制修正
            AlignTransformPosition(nutMountPoint, nutTightenedPosition);
        }
        
        Debug.Log("螺母动画完成 ========================");
    }

    // 新增：以挂载点为基准移动物体
    IEnumerator MoveAndRotateByMountPoint(Transform target, Transform mountPoint, Vector3 targetMountPointPos, Quaternion targetRotation, float duration)
    {
        if (target == null || mountPoint == null)
        {
            Debug.LogError("目标或挂载点为空！");
            yield break;
        }
        
        float elapsed = 0f;
        
        // 记录初始状态
        Vector3 startMountPointPos = mountPoint.position;
        Quaternion startRotation = target.rotation;
        
        // 计算需要应用的偏移
        Vector3 objectOffset = target.position - mountPoint.position;
        
        while (elapsed < duration)
        {
            elapsed += Time.deltaTime;
            float t = Mathf.SmoothStep(0f, 1f, elapsed / duration);
            
            // 1. 插值计算挂载点的新位置
            Vector3 newMountPointPos = Vector3.Lerp(startMountPointPos, targetMountPointPos, t);
            
            // 2. 设置挂载点位置
            mountPoint.position = newMountPointPos;
            
            // 3. 应用旋转
            target.rotation = Quaternion.Slerp(startRotation, targetRotation, t);
            
            // 4. 更新物体位置以保持原有的偏移
            target.position = mountPoint.position + target.rotation * Quaternion.Inverse(startRotation) * objectOffset;
            
            yield return null;
        }
        
        // 确保最终位置和旋转准确
        mountPoint.position = targetMountPointPos;
        target.rotation = targetRotation;
        
        // 根据偏移计算最终物体位置
        target.position = mountPoint.position + target.rotation * Quaternion.Inverse(startRotation) * objectOffset;
        
        Debug.Log($"MoveAndRotateByMountPoint完成 - 目标: {target.name}, 挂载点: {mountPoint.name}");
        Debug.Log($"  挂载点最终位置: {mountPoint.position}, 目标最终位置: {target.position}");
    }
    
    // 新增：仅移动挂载点，物体跟随
    IEnumerator MoveByMountPoint(Transform target, Transform mountPoint, Vector3 targetMountPointPos, float duration)
    {
        if (target == null || mountPoint == null)
        {
            Debug.LogError("目标或挂载点为空！");
            yield break;
        }
        
        float elapsed = 0f;
        
        // 记录初始状态
        Vector3 startMountPointPos = mountPoint.position;
        
        // 计算初始偏移
        Vector3 initialOffset = target.position - mountPoint.position;
        
        while (elapsed < duration)
        {
            elapsed += Time.deltaTime;
            float t = Mathf.SmoothStep(0f, 1f, elapsed / duration);
            
            // 1. 插值计算挂载点的新位置
            Vector3 newMountPointPos = Vector3.Lerp(startMountPointPos, targetMountPointPos, t);
            
            // 2. 设置挂载点位置
            mountPoint.position = newMountPointPos;
            
            // 3. 更新物体位置，保持相对挂载点的偏移
            target.position = mountPoint.position + initialOffset;
            
            yield return null;
        }
        
        // 确保最终位置准确
        mountPoint.position = targetMountPointPos;
        target.position = mountPoint.position + initialOffset;
        
        Debug.Log($"MoveByMountPoint完成 - 目标: {target.name}, 挂载点: {mountPoint.name}");
        Debug.Log($"  挂载点最终位置: {mountPoint.position}, 目标最终位置: {target.position}");
    }
    
    // 新增：强制对齐变换位置
    void AlignTransformPosition(Transform transform, Vector3 targetPosition)
    {
        if (transform == null) return;
        
        transform.position = targetPosition;
        Debug.Log($"强制对齐 {transform.name} 到位置 {targetPosition}");
    }
}