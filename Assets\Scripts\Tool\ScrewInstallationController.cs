using UnityEngine;
using System.Collections;

/// <summary>
/// 螺丝安装控制器示例
/// 展示如何使用新的螺丝落点控制功能
/// </summary>
public class ScrewInstallationController : MonoBehaviour
{
    [Header("组件引用")]
    [SerializeField] private AssemblyAnimationManager animationManager;
    [SerializeField] private AssemblyPart screwPart;
    [SerializeField] private Transform targetHole;
    
    [Header("测试设置")]
    [SerializeField] private Vector3 holeDirection = Vector3.forward;
    [SerializeField] private float animationDuration = 2.0f;
    
    [Header("插入深度测试")]
    [SerializeField] private float testInsertionDepth = 0.03f;
    [SerializeField] private float testInsertionRatio = 0.8f;
    
    void Start()
    {
        if (animationManager == null)
            animationManager = FindObjectOfType<AssemblyAnimationManager>();
    }

    void Update()
    {
        // 测试不同的螺丝安装方式
        if (Input.GetKeyDown(KeyCode.Alpha1))
        {
            TestDefaultInstallation();
        }
        else if (Input.GetKeyDown(KeyCode.Alpha2))
        {
            TestFixedDepthInstallation();
        }
        else if (Input.GetKeyDown(KeyCode.Alpha3))
        {
            TestRatioBasedInstallation();
        }
        else if (Input.GetKeyDown(KeyCode.Alpha4))
        {
            TestCustomDepthInstallation();
        }
        else if (Input.GetKeyDown(KeyCode.R))
        {
            ResetScrewPosition();
        }
    }

    /// <summary>
    /// 测试默认安装方式
    /// </summary>
    void TestDefaultInstallation()
    {
        Debug.Log("🔧 测试1: 默认安装方式");
        StartCoroutine(animationManager.InstallScrew(screwPart, targetHole, holeDirection, animationDuration));
    }

    /// <summary>
    /// 测试固定深度安装
    /// </summary>
    void TestFixedDepthInstallation()
    {
        Debug.Log($"🔧 测试2: 固定深度安装 ({testInsertionDepth:F3}m)");
        
        // 设置为固定深度模式
        animationManager.SetScrewInsertionDepth(testInsertionDepth);
        
        StartCoroutine(animationManager.InstallScrew(screwPart, targetHole, holeDirection, animationDuration));
    }

    /// <summary>
    /// 测试比例模式安装
    /// </summary>
    void TestRatioBasedInstallation()
    {
        Debug.Log($"🔧 测试3: 比例模式安装 ({testInsertionRatio:F1}倍螺丝长度)");
        
        // 设置为比例模式
        animationManager.SetScrewInsertionRatio(testInsertionRatio);
        
        StartCoroutine(animationManager.InstallScrew(screwPart, targetHole, holeDirection, animationDuration));
    }

    /// <summary>
    /// 测试自定义深度安装
    /// </summary>
    void TestCustomDepthInstallation()
    {
        Debug.Log($"🔧 测试4: 自定义深度安装 ({testInsertionDepth:F3}m)");
        
        StartCoroutine(animationManager.InstallScrewWithDepth(
            screwPart, 
            targetHole, 
            holeDirection, 
            testInsertionDepth, 
            animationDuration
        ));
    }

    /// <summary>
    /// 重置螺丝位置
    /// </summary>
    void ResetScrewPosition()
    {
        if (screwPart != null)
        {
            // 将螺丝移动到孔位上方
            Vector3 resetPosition = targetHole.position + Vector3.up * 0.1f;
            screwPart.PartTransform.position = resetPosition;
            screwPart.PartTransform.rotation = Quaternion.identity;
            Debug.Log("🔄 螺丝位置已重置");
        }
    }

    /// <summary>
    /// 动态调整插入深度
    /// </summary>
    /// <param name="depth">新的插入深度</param>
    public void SetInsertionDepth(float depth)
    {
        testInsertionDepth = Mathf.Max(0, depth);
        Debug.Log($"🔧 设置插入深度: {testInsertionDepth:F3}m");
    }

    /// <summary>
    /// 动态调整插入比例
    /// </summary>
    /// <param name="ratio">新的插入比例</param>
    public void SetInsertionRatio(float ratio)
    {
        testInsertionRatio = Mathf.Clamp01(ratio);
        Debug.Log($"🔧 设置插入比例: {testInsertionRatio:F2}");
    }

    void OnGUI()
    {
        GUILayout.BeginArea(new Rect(10, 10, 300, 200));
        GUILayout.Label("螺丝安装测试控制");
        GUILayout.Space(10);
        
        GUILayout.Label("按键控制:");
        GUILayout.Label("1 - 默认安装");
        GUILayout.Label("2 - 固定深度安装");
        GUILayout.Label("3 - 比例模式安装");
        GUILayout.Label("4 - 自定义深度安装");
        GUILayout.Label("R - 重置螺丝位置");
        
        GUILayout.Space(10);
        GUILayout.Label($"当前设置:");
        GUILayout.Label($"插入深度: {testInsertionDepth:F3}m");
        GUILayout.Label($"插入比例: {testInsertionRatio:F2}");
        
        GUILayout.EndArea();
    }
}
