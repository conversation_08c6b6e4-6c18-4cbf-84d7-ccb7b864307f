using UnityEngine;
using System.Collections.Generic;

/// <summary>
/// 参考点调试工具 - 用于分析参考点索引映射问题
/// </summary>
public class ReferencePointDebugger : MonoBehaviour
{
    [Header("调试设置")]
    [SerializeField] private string targetPartName = "安装连接块反1";
    [SerializeField] private bool debugOnStart = true;
    
    private Neo4jAssemblyController assemblyController;
    private Dictionary<string, AssemblyPart> partNameToComponent = new Dictionary<string, AssemblyPart>();
    private Dictionary<string, int> referencePointNameToIndex = new Dictionary<string, int>();

    void Start()
    {
        if (debugOnStart)
        {
            DebugReferencePointMapping();
        }
    }

    void Update()
    {
        if (Input.GetKeyDown(KeyCode.F1))
        {
            DebugReferencePointMapping();
        }
        
        if (Input.GetKeyDown(KeyCode.F2))
        {
            TestSpecificPart(targetPartName);
        }
    }

    /// <summary>
    /// 调试参考点映射
    /// </summary>
    public void DebugReferencePointMapping()
    {
        Debug.Log("=== 开始调试参考点映射 ===");
        
        // 查找装配控制器
        assemblyController = FindObjectOfType<Neo4jAssemblyController>();
        if (assemblyController == null)
        {
            Debug.LogError("未找到Neo4jAssemblyController!");
            return;
        }

        // 重新初始化映射
        InitializePartMapping();
        
        // 测试特定零件
        TestSpecificPart(targetPartName);
        
        Debug.Log("=== 参考点映射调试完成 ===");
    }

    /// <summary>
    /// 初始化零件映射（模拟Neo4jAssemblyController的逻辑）
    /// </summary>
    private void InitializePartMapping()
    {
        partNameToComponent.Clear();
        referencePointNameToIndex.Clear();
        
        // 查找场景中所有的AssemblyPart组件
        AssemblyPart[] allParts = FindObjectsOfType<AssemblyPart>();
        Debug.Log($"找到 {allParts.Length} 个AssemblyPart组件");

        foreach (var part in allParts)
        {
            string partName = part.gameObject.name;
            partNameToComponent[partName] = part;
            Debug.Log($"🔧 零件: {partName}");

            // 初始化参考点映射
            for (int i = 0; i < part.ReferencePointCount; i++)
            {
                Transform refPoint = part.GetReferencePoint(i);
                if (refPoint != null)
                {
                    string refName = refPoint.name;
                    string fullRefName = $"{partName}_{refName}";
                    referencePointNameToIndex[fullRefName] = i;
                    
                    Debug.Log($"  📍 参考点 {i}: {refName} -> 映射键: {fullRefName}");
                    
                    // 如果参考点名称是"P1"格式，也添加"{partName}-P1"格式的映射
                    if (refName.StartsWith("P") && int.TryParse(refName.Substring(1), out _))
                    {
                        string altRefName = $"{partName}-{refName}";
                        referencePointNameToIndex[altRefName] = i;
                        Debug.Log($"    🔄 备用映射: {altRefName} -> 索引 {i}");
                    }
                }
                else
                {
                    Debug.LogWarning($"  ❌ 参考点 {i} 为null!");
                }
            }
        }
    }

    /// <summary>
    /// 测试特定零件的参考点解析
    /// </summary>
    private void TestSpecificPart(string partName)
    {
        Debug.Log($"\n=== 测试零件: {partName} ===");
        
        if (!partNameToComponent.TryGetValue(partName, out AssemblyPart part))
        {
            Debug.LogError($"未找到零件: {partName}");
            return;
        }

        Debug.Log($"零件 {partName} 有 {part.ReferencePointCount} 个参考点:");
        
        // 显示所有参考点的实际名称
        for (int i = 0; i < part.ReferencePointCount; i++)
        {
            Transform refPoint = part.GetReferencePoint(i);
            if (refPoint != null)
            {
                Debug.Log($"  索引 {i}: 名称='{refPoint.name}', 位置={refPoint.position}");
            }
        }

        // 测试常见的参考点名称格式
        string[] testFormats = {
            $"{partName}_P0",
            $"{partName}_P1",
            $"{partName}_P2",
            $"{partName}_Sphere",
            $"{partName}_Sphere (1)",
            $"{partName}_Sphere (2)",
            $"{partName}-P0",
            $"{partName}-P1",
            $"{partName}-P2"
        };

        Debug.Log("\n测试参考点名称解析:");
        foreach (string testName in testFormats)
        {
            int index = GetReferencePointIndex(partName, testName);
            Debug.Log($"  '{testName}' -> 索引 {index}");
        }
    }

    /// <summary>
    /// 模拟Neo4jAssemblyController的GetReferencePointIndex方法
    /// </summary>
    private int GetReferencePointIndex(string partName, string refPointName)
    {
        // 处理Neo4j中可能的格式差异
        string cleanRefName = refPointName;
        string refPartName = null;

        Debug.Log($"🔍 解析参考点: partName='{partName}', refPointName='{refPointName}'");

        // 检查参考点名称是否包含"_P"（格式：零件名称_P1）
        int lastPIndex = refPointName.LastIndexOf("_P");
        if (lastPIndex > 0 && lastPIndex < refPointName.Length - 2)
        {
            // 提取零件名和参考点部分
            refPartName = refPointName.Substring(0, lastPIndex);
            cleanRefName = refPointName.Substring(lastPIndex + 1); // +1 to skip the underscore

            Debug.Log($"  📝 从完整格式解析: 零件名='{refPartName}', 参考点='{cleanRefName}'");

            // 检查零件名是否匹配
            if (refPartName != partName)
            {
                Debug.LogWarning($"  ⚠️ 零件名不匹配: 期望'{partName}', 实际'{refPartName}'");
            }
        }
        // 如果参考点名称包含分隔符(如"-")，提取后半部分
        else if (refPointName.Contains("-"))
        {
            string[] parts = refPointName.Split('-');
            cleanRefName = parts[parts.Length - 1]; // 取最后一部分，通常是"P1"
            Debug.Log($"  📝 从分隔符格式解析: '{refPointName}' -> '{cleanRefName}'");
        }

        // 尝试直接从映射中获取
        string fullRefName = $"{partName}_{cleanRefName}";
        if (referencePointNameToIndex.TryGetValue(fullRefName, out int index))
        {
            Debug.Log($"  ✅ 通过映射找到: '{fullRefName}' -> 索引 {index}");
            return index;
        }

        // 尝试直接使用原始名称从映射中获取
        if (referencePointNameToIndex.TryGetValue(refPointName, out index))
        {
            Debug.Log($"  ✅ 通过原始名称找到: '{refPointName}' -> 索引 {index}");
            return index;
        }

        // 如果找不到，尝试解析参考点名称中的索引
        // 假设参考点名称格式为 "P0", "P1", "P2" 等，数字直接对应索引
        if (cleanRefName.StartsWith("P") && int.TryParse(cleanRefName.Substring(1), out int parsedIndex))
        {
            // 验证索引是否在有效范围内
            if (partNameToComponent.TryGetValue(partName, out AssemblyPart part) &&
                parsedIndex >= 0 && parsedIndex < part.ReferencePointCount)
            {
                Debug.Log($"  ✅ 通过直接解析找到: '{cleanRefName}' -> 索引 {parsedIndex}");
                return parsedIndex;
            }
            else
            {
                Debug.LogWarning($"  ⚠️ 解析的索引 {parsedIndex} 超出范围 (0-{(part?.ReferencePointCount ?? 0) - 1})");
            }
        }

        // 如果都失败，返回-1
        Debug.LogWarning($"  ❌ 无法解析参考点: fullRefName='{fullRefName}', 原始名称='{refPointName}'");
        
        // 显示所有可用的映射键
        Debug.Log("  📋 可用的映射键:");
        foreach (var kvp in referencePointNameToIndex)
        {
            if (kvp.Key.StartsWith(partName))
            {
                Debug.Log($"    '{kvp.Key}' -> {kvp.Value}");
            }
        }
        
        return -1;
    }
}
