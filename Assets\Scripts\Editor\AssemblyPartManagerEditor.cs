using UnityEngine;
using UnityEditor;
using UnityEngine.UI;

/// <summary>
/// 装配零件管理器编辑器
/// 提供便捷的设置和测试功能
/// </summary>
[CustomEditor(typeof(AssemblyPartManager))]
public class AssemblyPartManagerEditor : Editor
{
    private AssemblyPartManager partManager;
    private bool showTestControls = true;
    private bool showStatusInfo = true;
    
    void OnEnable()
    {
        partManager = (AssemblyPartManager)target;
    }
    
    public override void OnInspectorGUI()
    {
        // 绘制默认Inspector
        DrawDefaultInspector();
        
        EditorGUILayout.Space(10);
        EditorGUILayout.LabelField("零件管理器控制", EditorStyles.boldLabel);
        
        // 快速设置区域
        DrawQuickSetup();
        
        // 测试控制区域
        DrawTestControls();
        
        // 状态信息区域
        DrawStatusInfo();
        
        // 工具按钮区域
        DrawToolButtons();
    }
    
    /// <summary>
    /// 绘制快速设置区域
    /// </summary>
    private void DrawQuickSetup()
    {
        EditorGUILayout.BeginVertical("box");
        EditorGUILayout.LabelField("快速设置", EditorStyles.miniBoldLabel);
        
        EditorGUILayout.BeginHorizontal();
        
        if (GUILayout.Button("🔍 自动查找文本框", GUILayout.Height(25)))
        {
            AutoFindStepInfoText();
        }
        
        if (GUILayout.Button("📝 创建文本框", GUILayout.Height(25)))
        {
            CreateStepInfoText();
        }
        
        EditorGUILayout.EndHorizontal();
        
        EditorGUILayout.BeginHorizontal();
        
        if (GUILayout.Button("🔧 添加测试工具", GUILayout.Height(25)))
        {
            AddTestTool();
        }
        
        if (GUILayout.Button("🎯 聚焦到管理器", GUILayout.Height(25)))
        {
            FocusOnManager();
        }
        
        EditorGUILayout.EndHorizontal();
        
        EditorGUILayout.EndVertical();
        EditorGUILayout.Space();
    }
    
    /// <summary>
    /// 绘制测试控制区域
    /// </summary>
    private void DrawTestControls()
    {
        showTestControls = EditorGUILayout.Foldout(showTestControls, "测试控制", true);
        if (showTestControls)
        {
            EditorGUILayout.BeginVertical("box");
            
            EditorGUILayout.LabelField("零件控制:", EditorStyles.miniBoldLabel);
            
            EditorGUILayout.BeginHorizontal();
            
            GUI.backgroundColor = Color.red;
            if (GUILayout.Button("🙈 隐藏所有零件", GUILayout.Height(30)))
            {
                if (Application.isPlaying)
                {
                    partManager.HideAllPartsExceptAlwaysVisible();
                }
                else
                {
                    Debug.LogWarning("请在运行时测试此功能");
                }
            }
            
            GUI.backgroundColor = Color.green;
            if (GUILayout.Button("🔄 重置零件状态", GUILayout.Height(30)))
            {
                if (Application.isPlaying)
                {
                    partManager.ResetAllParts();
                }
                else
                {
                    Debug.LogWarning("请在运行时测试此功能");
                }
            }
            
            GUI.backgroundColor = Color.white;
            EditorGUILayout.EndHorizontal();
            
            EditorGUILayout.Space();
            
            EditorGUILayout.LabelField("测试装配步骤:", EditorStyles.miniBoldLabel);
            
            EditorGUILayout.BeginHorizontal();
            if (GUILayout.Button("测试步骤1", GUILayout.Height(25)))
            {
                TestAssemblyStep("左抓手", "右抓手", "SCREW", "M6螺丝", "M6螺母");
            }
            
            if (GUILayout.Button("测试步骤2", GUILayout.Height(25)))
            {
                TestAssemblyStep("连接块", "中下段", "DIRECT", "", "");
            }
            EditorGUILayout.EndHorizontal();
            
            EditorGUILayout.EndVertical();
        }
        EditorGUILayout.Space();
    }
    
    /// <summary>
    /// 绘制状态信息区域
    /// </summary>
    private void DrawStatusInfo()
    {
        showStatusInfo = EditorGUILayout.Foldout(showStatusInfo, "状态信息", true);
        if (showStatusInfo)
        {
            EditorGUILayout.BeginVertical("helpbox");
            
            if (Application.isPlaying && partManager != null)
            {
                EditorGUILayout.LabelField("运行时状态:", EditorStyles.miniBoldLabel);
                EditorGUILayout.LabelField(partManager.GetStatusInfo());
                
                var visibleParts = partManager.GetVisibleParts();
                EditorGUILayout.LabelField($"当前可见零件: {visibleParts.Count}");
                
                if (visibleParts.Count > 0)
                {
                    EditorGUILayout.LabelField("可见零件列表:");
                    foreach (string partName in visibleParts)
                    {
                        EditorGUILayout.LabelField($"  • {partName}");
                    }
                }
            }
            else
            {
                // 编辑器模式下的静态信息
                AssemblyPart[] allParts = FindObjectsOfType<AssemblyPart>();
                EditorGUILayout.LabelField("编辑器模式状态:", EditorStyles.miniBoldLabel);
                EditorGUILayout.LabelField($"场景中零件总数: {allParts.Length}");
                
                // 检查始终显示的零件
                SerializedProperty alwaysVisibleProp = serializedObject.FindProperty("alwaysVisibleParts");
                if (alwaysVisibleProp != null)
                {
                    int alwaysVisibleCount = 0;
                    for (int i = 0; i < alwaysVisibleProp.arraySize; i++)
                    {
                        string alwaysVisibleName = alwaysVisibleProp.GetArrayElementAtIndex(i).stringValue;
                        foreach (AssemblyPart part in allParts)
                        {
                            if (part.PartName.Contains(alwaysVisibleName))
                            {
                                alwaysVisibleCount++;
                                break;
                            }
                        }
                    }
                    EditorGUILayout.LabelField($"始终显示零件数: {alwaysVisibleCount}");
                    EditorGUILayout.LabelField($"可管理零件数: {allParts.Length - alwaysVisibleCount}");
                }
            }
            
            EditorGUILayout.EndVertical();
        }
        EditorGUILayout.Space();
    }
    
    /// <summary>
    /// 绘制工具按钮区域
    /// </summary>
    private void DrawToolButtons()
    {
        EditorGUILayout.BeginVertical("box");
        EditorGUILayout.LabelField("工具", EditorStyles.miniBoldLabel);
        
        EditorGUILayout.BeginHorizontal();
        
        if (GUILayout.Button("📋 生成零件列表", GUILayout.Height(25)))
        {
            GeneratePartsList();
        }
        
        if (GUILayout.Button("🔍 验证设置", GUILayout.Height(25)))
        {
            ValidateSetup();
        }
        
        EditorGUILayout.EndHorizontal();
        
        EditorGUILayout.EndVertical();
        
        // 提示信息
        EditorGUILayout.Space();
        EditorGUILayout.HelpBox(
            "💡 使用提示:\n" +
            "• 确保场景中有Text组件用于显示装配步骤信息\n" +
            "• 在'始终显示零件'中添加不需要隐藏的零件名称（如'中下段'）\n" +
            "• 运行时可以使用测试按钮验证功能\n" +
            "• 装配系统会自动调用零件管理功能", 
            MessageType.Info);
    }
    
    /// <summary>
    /// 自动查找步骤信息文本框
    /// </summary>
    private void AutoFindStepInfoText()
    {
        Text[] allTexts = FindObjectsOfType<Text>();
        Text foundText = null;
        
        foreach (Text text in allTexts)
        {
            if (text.name.ToLower().Contains("step") || 
                text.name.ToLower().Contains("info") ||
                text.name.ToLower().Contains("装配"))
            {
                foundText = text;
                break;
            }
        }
        
        if (foundText != null)
        {
            SerializedProperty stepInfoTextProp = serializedObject.FindProperty("stepInfoText");
            stepInfoTextProp.objectReferenceValue = foundText;
            serializedObject.ApplyModifiedProperties();
            
            Debug.Log($"✅ 找到并设置步骤信息文本框: {foundText.name}");
            EditorUtility.SetDirty(partManager);
        }
        else
        {
            Debug.LogWarning("⚠️ 未找到合适的文本框，请手动设置或创建新的文本框");
        }
    }
    
    /// <summary>
    /// 创建步骤信息文本框
    /// </summary>
    private void CreateStepInfoText()
    {
        // 查找或创建Canvas
        Canvas canvas = FindObjectOfType<Canvas>();
        if (canvas == null)
        {
            GameObject canvasObj = new GameObject("AssemblyCanvas");
            canvas = canvasObj.AddComponent<Canvas>();
            canvas.renderMode = RenderMode.ScreenSpaceOverlay;
            canvasObj.AddComponent<CanvasScaler>();
            canvasObj.AddComponent<GraphicRaycaster>();
            
            Undo.RegisterCreatedObjectUndo(canvasObj, "Create Assembly Canvas");
        }
        
        // 创建文本对象
        GameObject textObj = new GameObject("AssemblyStepInfoText");
        textObj.transform.SetParent(canvas.transform, false);
        
        Text stepInfoText = textObj.AddComponent<Text>();
        stepInfoText.font = Resources.GetBuiltinResource<Font>("Arial.ttf");
        stepInfoText.fontSize = 16;
        stepInfoText.color = Color.white;
        stepInfoText.text = "装配步骤信息将在这里显示...";
        
        // 设置RectTransform
        RectTransform rectTransform = stepInfoText.GetComponent<RectTransform>();
        rectTransform.anchorMin = new Vector2(0, 1);
        rectTransform.anchorMax = new Vector2(0, 1);
        rectTransform.pivot = new Vector2(0, 1);
        rectTransform.anchoredPosition = new Vector2(10, -10);
        rectTransform.sizeDelta = new Vector2(400, 200);
        
        // 设置到PartManager
        SerializedProperty stepInfoTextProp = serializedObject.FindProperty("stepInfoText");
        stepInfoTextProp.objectReferenceValue = stepInfoText;
        serializedObject.ApplyModifiedProperties();
        
        Undo.RegisterCreatedObjectUndo(textObj, "Create Step Info Text");
        
        Debug.Log("✅ 已创建装配步骤信息文本框");
        EditorUtility.SetDirty(partManager);
    }
    
    /// <summary>
    /// 添加测试工具
    /// </summary>
    private void AddTestTool()
    {
        Debug.Log("💡 测试工具功能已移除，请直接使用Inspector中的测试按钮进行测试");
    }
    
    /// <summary>
    /// 聚焦到管理器
    /// </summary>
    private void FocusOnManager()
    {
        Selection.activeGameObject = partManager.gameObject;
        SceneView.FrameLastActiveSceneView();
    }
    
    /// <summary>
    /// 测试装配步骤
    /// </summary>
    private void TestAssemblyStep(string movingPart, string targetPart, string fastenerType, string screwType, string nutType)
    {
        if (Application.isPlaying)
        {
            partManager.ProcessAssemblyStep(movingPart, targetPart, fastenerType, screwType, nutType);
            Debug.Log($"🔧 测试装配步骤: {movingPart} -> {targetPart}");
        }
        else
        {
            Debug.LogWarning("请在运行时测试装配步骤功能");
        }
    }
    
    /// <summary>
    /// 生成零件列表
    /// </summary>
    private void GeneratePartsList()
    {
        AssemblyPart[] allParts = FindObjectsOfType<AssemblyPart>();
        
        Debug.Log("📋 场景中的所有零件:");
        for (int i = 0; i < allParts.Length; i++)
        {
            Debug.Log($"  {i + 1}. {allParts[i].PartName} (GameObject: {allParts[i].gameObject.name})");
        }
        
        Debug.Log($"总计: {allParts.Length} 个零件");
    }
    
    /// <summary>
    /// 验证设置
    /// </summary>
    private void ValidateSetup()
    {
        bool isValid = true;
        
        // 检查步骤信息文本框
        SerializedProperty stepInfoTextProp = serializedObject.FindProperty("stepInfoText");
        if (stepInfoTextProp.objectReferenceValue == null)
        {
            Debug.LogWarning("⚠️ 步骤信息文本框未设置");
            isValid = false;
        }
        
        // 检查零件数量
        AssemblyPart[] allParts = FindObjectsOfType<AssemblyPart>();
        if (allParts.Length == 0)
        {
            Debug.LogWarning("⚠️ 场景中未找到AssemblyPart组件");
            isValid = false;
        }
        
        // 检查始终显示零件设置
        SerializedProperty alwaysVisibleProp = serializedObject.FindProperty("alwaysVisibleParts");
        if (alwaysVisibleProp.arraySize == 0)
        {
            Debug.LogWarning("⚠️ 未设置始终显示的零件");
        }
        
        if (isValid)
        {
            Debug.Log("✅ 装配零件管理器设置验证通过");
        }
        else
        {
            Debug.LogError("❌ 装配零件管理器设置存在问题，请检查上述警告");
        }
    }
}
