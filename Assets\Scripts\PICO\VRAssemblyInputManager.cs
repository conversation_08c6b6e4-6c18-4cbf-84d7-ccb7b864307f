using UnityEngine;
using System.Collections;

/// <summary>
/// VR装配输入管理器
/// 处理用户输入，触发装配区域的视角调整功能
/// </summary>
public class VRAssemblyInputManager : MonoBehaviour
{
    [Header("组件引用")]
    [SerializeField] private VRAssemblyPositioner positioner;

    [Header("输入设置")]
    [SerializeField] private KeyCode adjustViewKey = KeyCode.Space; // 调整视角的按键
    [SerializeField] private bool enableInput = true; // 是否启用输入

    [Header("调试设置")]
    [SerializeField] private bool showDebugInfo = true; // 显示调试信息

    void Start()
    {
        // 自动查找VRAssemblyPositioner组件
        if (positioner == null)
        {
            positioner = FindObjectOfType<VRAssemblyPositioner>();
            if (positioner == null)
            {
                Debug.LogError("[VRAssemblyInputManager] 未找到VRAssemblyPositioner组件！");
            }
            else
            {
                Debug.Log("[VRAssemblyInputManager] 自动找到VRAssemblyPositioner组件");
            }
        }

        if (showDebugInfo)
        {
            Debug.Log($"[VRAssemblyInputManager] 初始化完成");
            Debug.Log($"   调整视角按键: {adjustViewKey}");
            Debug.Log($"   输入启用状态: {enableInput}");
        }
    }

    void Update()
    {
        if (!enableInput || positioner == null) return;

        HandleUserInput();
    }

    /// <summary>
    /// 处理用户输入
    /// </summary>
    private void HandleUserInput()
    {
        // 检测调整视角按键
        if (Input.GetKeyDown(adjustViewKey))
        {
            TriggerViewAdjustment();
        }
    }

    /// <summary>
    /// 触发视角调整（第一部分功能）
    /// </summary>
    public void TriggerViewAdjustment()
    {
        if (positioner == null)
        {
            Debug.LogError("[VRAssemblyInputManager] VRAssemblyPositioner组件未设置！");
            return;
        }

        if (showDebugInfo)
        {
            Debug.Log($"[VRAssemblyInputManager] 用户按下 {adjustViewKey} 键，触发视角调整");
        }

        StartCoroutine(positioner.AdjustViewForUser());
    }

    /// <summary>
    /// 启用/禁用输入
    /// </summary>
    public void SetInputEnabled(bool enabled)
    {
        enableInput = enabled;
        if (showDebugInfo)
        {
            Debug.Log($"[VRAssemblyInputManager] 输入状态设置为: {(enabled ? "启用" : "禁用")}");
        }
    }

    /// <summary>
    /// 设置调整视角的按键
    /// </summary>
    public void SetAdjustViewKey(KeyCode newKey)
    {
        adjustViewKey = newKey;
        if (showDebugInfo)
        {
            Debug.Log($"[VRAssemblyInputManager] 调整视角按键设置为: {newKey}");
        }
    }

    /// <summary>
    /// 获取当前输入状态
    /// </summary>
    public bool IsInputEnabled()
    {
        return enableInput;
    }

    /// <summary>
    /// 获取当前调整视角按键
    /// </summary>
    public KeyCode GetAdjustViewKey()
    {
        return adjustViewKey;
    }

    /// <summary>
    /// 显示当前配置信息
    /// </summary>
    [ContextMenu("显示配置信息")]
    public void ShowConfigInfo()
    {
        Debug.Log("=== VRAssemblyInputManager 配置信息 ===");
        Debug.Log($"   VRAssemblyPositioner: {(positioner != null ? "已连接" : "未连接")}");
        Debug.Log($"   调整视角按键: {adjustViewKey}");
        Debug.Log($"   输入启用状态: {enableInput}");
        Debug.Log($"   调试信息显示: {showDebugInfo}");
        Debug.Log("=== 配置信息结束 ===");
    }

    /// <summary>
    /// 手动触发视角调整（用于UI按钮等）
    /// </summary>
    [ContextMenu("手动触发视角调整")]
    public void ManualTriggerViewAdjustment()
    {
        TriggerViewAdjustment();
    }
}
