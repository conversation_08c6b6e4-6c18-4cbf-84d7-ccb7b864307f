using System.Collections.Generic;
using UnityEngine;

/// <summary>
/// 装配步骤数据结构
/// 定义装配过程中的单个步骤信息
/// </summary>
[System.Serializable]
public struct AssemblyStep
{
    public string movingPartName;           // 移动零件名称
    public string targetPartName;           // 目标零件名称
    public string movingPartRefPoint;       // 移动零件参考点
    public string targetPartRefPoint;       // 目标零件参考点
    public string connectionType;           // 连接类型：DIRECT, SCREW, SNAP等
    public FastenerInfo fastener;           // 紧固件信息
    public List<AdditionalMountPoint> additionalMountPoints; // 额外装配点列表

    /// <summary>
    /// 紧固件信息结构
    /// </summary>
    [System.Serializable]
    public struct FastenerInfo
    {
        public string type;                 // 紧固件类型：SCREW_NUT, SCREW_ONLY, NONE等
        public string screwType;            // 螺丝类型：M2X6, BA_5等
        public string nutType;              // 螺母类型：nut等
        public string screwPrefabName;      // 螺丝预制体名称
        public string nutPrefabName;        // 螺母预制体名称

        /// <summary>
        /// 创建空的紧固件信息
        /// </summary>
        public static FastenerInfo Empty => new FastenerInfo
        {
            type = "NONE",
            screwType = "",
            nutType = "",
            screwPrefabName = "",
            nutPrefabName = ""
        };

        /// <summary>
        /// 创建螺丝紧固件信息
        /// </summary>
        public static FastenerInfo CreateScrew(string screwType, string nutType = "")
        {
            return new FastenerInfo
            {
                type = string.IsNullOrEmpty(nutType) ? "SCREW_ONLY" : "SCREW_NUT",
                screwType = screwType,
                nutType = nutType,
                screwPrefabName = GetScrewPrefabName(screwType),
                nutPrefabName = string.IsNullOrEmpty(nutType) ? "" : GetNutPrefabName(nutType)
            };
        }

        /// <summary>
        /// 根据螺丝类型获取预制体名称
        /// </summary>
        private static string GetScrewPrefabName(string screwType)
        {
            switch (screwType)
            {
                case "M2X6":
                    return "ScrewM2X6";
                case "BA_5":
                    return "ScrewBA_5";
                case "M3X10-10":
                    return "ScrewM3X10-10";
                case "M3X5-5":
                    return "ScrewM3X5-5";
                case "M3X16-16":
                    return "ScrewM3X16-16";
                case "ST2.9X6.5":
                    return "ScrewST2.9X6.5";
                case "M3X8-6.35":
                    return "ScrewM3X8-6.35";
                default:
                    return "ScrewM2X6"; // 默认螺丝
            }
        }

        /// <summary>
        /// 根据螺母类型获取预制体名称
        /// </summary>
        private static string GetNutPrefabName(string nutType)
        {
            switch (nutType)
            {
                case "nut":
                    return "Nut";
                case "Nut":
                    return "Nut";
                case "nut m3-c":
                    return "NutM3-C";
                case "":
                case null:
                    return ""; // 空螺母类型
                default:
                    return "Nut"; // 默认螺母
            }
        }
    }

    /// <summary>
    /// 额外装配点结构
    /// </summary>
    [System.Serializable]
    public struct AdditionalMountPoint
    {
        public string fromPoint;            // 源零件参考点
        public string toPoint;              // 目标零件参考点
        public string connectionType;       // 连接类型
        public FastenerInfo fastener;       // 紧固件信息

        /// <summary>
        /// 创建额外装配点
        /// </summary>
        public static AdditionalMountPoint Create(string fromPoint, string toPoint, 
            string connectionType = "SCREW", FastenerInfo fastener = default)
        {
            return new AdditionalMountPoint
            {
                fromPoint = fromPoint,
                toPoint = toPoint,
                connectionType = connectionType,
                fastener = fastener
            };
        }
    }

    /// <summary>
    /// 创建简单的装配步骤（直接连接）
    /// </summary>
    public static AssemblyStep CreateDirect(string movingPart, string targetPart, 
        string movingRef, string targetRef)
    {
        return new AssemblyStep
        {
            movingPartName = movingPart,
            targetPartName = targetPart,
            movingPartRefPoint = movingRef,
            targetPartRefPoint = targetRef,
            connectionType = "DIRECT",
            fastener = FastenerInfo.Empty,
            additionalMountPoints = new List<AdditionalMountPoint>()
        };
    }

    /// <summary>
    /// 创建带螺丝的装配步骤
    /// </summary>
    public static AssemblyStep CreateWithScrew(string movingPart, string targetPart,
        string movingRef, string targetRef, string screwType, string nutType = "")
    {
        return new AssemblyStep
        {
            movingPartName = movingPart,
            targetPartName = targetPart,
            movingPartRefPoint = movingRef,
            targetPartRefPoint = targetRef,
            connectionType = "SCREW",
            fastener = FastenerInfo.CreateScrew(screwType, nutType),
            additionalMountPoints = new List<AdditionalMountPoint>()
        };
    }

    /// <summary>
    /// 创建完整的装配步骤
    /// </summary>
    public static AssemblyStep Create(string movingPart, string targetPart, 
        string movingRef, string targetRef, string connectionType, FastenerInfo fastenerInfo)
    {
        return new AssemblyStep
        {
            movingPartName = movingPart,
            targetPartName = targetPart,
            movingPartRefPoint = movingRef,
            targetPartRefPoint = targetRef,
            connectionType = connectionType,
            fastener = fastenerInfo,
            additionalMountPoints = new List<AdditionalMountPoint>()
        };
    }

    /// <summary>
    /// 添加额外装配点
    /// </summary>
    public void AddMountPoint(string fromPoint, string toPoint, string screwType, string nutType = "")
    {
        if (additionalMountPoints == null)
        {
            additionalMountPoints = new List<AdditionalMountPoint>();
        }

        additionalMountPoints.Add(AdditionalMountPoint.Create(
            fromPoint, 
            toPoint, 
            "SCREW", 
            FastenerInfo.CreateScrew(screwType, nutType)
        ));
    }

    /// <summary>
    /// 获取步骤描述
    /// </summary>
    public override string ToString()
    {
        string description = $"{movingPartName}[{movingPartRefPoint}] -> {targetPartName}[{targetPartRefPoint}] ({connectionType})";
        
        if (fastener.type != "NONE" && !string.IsNullOrEmpty(fastener.screwType))
        {
            description += $" [螺丝: {fastener.screwType}";
            if (!string.IsNullOrEmpty(fastener.nutType))
            {
                description += $", 螺母: {fastener.nutType}";
            }
            description += "]";
        }

        if (additionalMountPoints != null && additionalMountPoints.Count > 0)
        {
            description += $" +{additionalMountPoints.Count}个额外装配点";
        }

        return description;
    }

    /// <summary>
    /// 检查是否为有效的装配步骤
    /// </summary>
    public bool IsValid()
    {
        return !string.IsNullOrEmpty(movingPartName) && 
               !string.IsNullOrEmpty(targetPartName) &&
               !string.IsNullOrEmpty(movingPartRefPoint) && 
               !string.IsNullOrEmpty(targetPartRefPoint);
    }

    /// <summary>
    /// 检查是否需要螺丝螺母
    /// </summary>
    public bool RequiresFastener()
    {
        return connectionType == "SCREW" && fastener.type != "NONE";
    }

    /// <summary>
    /// 检查是否有额外装配点
    /// </summary>
    public bool HasAdditionalMountPoints()
    {
        return additionalMountPoints != null && additionalMountPoints.Count > 0;
    }
}
