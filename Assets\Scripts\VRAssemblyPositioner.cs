using UnityEngine;
using System.Collections;

/// <summary>
/// VR装配位置调整器
/// 
/// 负责将装配区域移动到用户面前的最佳位置，
/// 并根据装配步骤调整零件朝向，确保用户能清楚看到装配过程
/// </summary>
public class VRAssemblyPositioner : MonoBehaviour
{
    [Header("VR视角优化设置")]
    [SerializeField] private Transform vrCamera;
    [SerializeField] private float repositionDuration = 2.0f;

    [Header("装配区域相对位置设置")]
    [SerializeField] private Vector3 relativePosition = new Vector3(-0.8f, 0.3f, 1.2f); // 相对于摄像机的位置偏移（左上角）

    [Header("VR模拟器支持")]
    [SerializeField] private MonoBehaviour vrSimulator; // VRSimulator组件
    [SerializeField] private bool useVRSimulator = true;
    
    [Header("装配区域设置")]
    [SerializeField] private Transform assemblyRoot;
    [SerializeField] private bool autoFindAssemblyRoot = true;
    
    [Header("调试设置")]
    [SerializeField] private bool showDebugGizmos = true;
    [SerializeField] private Color optimalPositionColor = Color.green;
    
    // 内部状态
    private Vector3 originalAssemblyPosition;
    private Quaternion originalAssemblyRotation;
    private bool isRepositioning = false;

    void Start()
    {
        InitializePositioner();
    }

    /// <summary>
    /// 初始化位置调整器
    /// </summary>
    private void InitializePositioner()
    {
        // 优先查找VR模拟器
        if (useVRSimulator && Application.isEditor)
        {
            if (vrSimulator == null)
            {
                vrSimulator = FindObjectOfType<VRSimulator>();
            }

            if (vrSimulator != null)
            {
                // 使用反射调用GetVRCamera方法
                var method = vrSimulator.GetType().GetMethod("GetVRCamera");
                if (method != null)
                {
                    var camera = method.Invoke(vrSimulator, null) as Camera;
                    if (camera != null)
                    {
                        vrCamera = camera.transform;
                        Debug.Log("[VRAssemblyPositioner] 使用VR模拟器摄像机");
                    }
                }
            }
        }

        // 自动查找VR摄像机
        if (vrCamera == null)
        {
            vrCamera = Camera.main != null ? Camera.main.transform : null;
            if (vrCamera == null)
            {
                Debug.LogWarning("[VRAssemblyPositioner] 未找到VR摄像机，请手动指定");
            }
        }

        // 自动查找装配根节点
        if (autoFindAssemblyRoot && assemblyRoot == null)
        {
            assemblyRoot = FindAssemblyRoot();
        }

        // 保存原始位置
        if (assemblyRoot != null)
        {
            originalAssemblyPosition = assemblyRoot.position;
            originalAssemblyRotation = assemblyRoot.rotation;
            
            Debug.Log($"[VRAssemblyPositioner] 装配根节点: {assemblyRoot.name}");
            Debug.Log($"[VRAssemblyPositioner] 原始位置: {originalAssemblyPosition}");
        }
    }

    /// <summary>
    /// 自动查找装配根节点
    /// </summary>
    private Transform FindAssemblyRoot()
    {
        // 查找包含AssemblyPart组件的父节点
        AssemblyPart[] assemblyParts = FindObjectsOfType<AssemblyPart>();
        if (assemblyParts.Length > 0)
        {
            Transform commonParent = FindCommonParent(assemblyParts);
            if (commonParent != null)
            {
                Debug.Log($"[VRAssemblyPositioner] 自动找到装配根节点: {commonParent.name}");
                return commonParent;
            }
        }

        Debug.LogWarning("[VRAssemblyPositioner] 未找到装配根节点，请手动指定");
        return null;
    }

    /// <summary>
    /// 查找多个AssemblyPart的共同父节点
    /// </summary>
    private Transform FindCommonParent(AssemblyPart[] parts)
    {
        if (parts.Length == 0) return null;
        if (parts.Length == 1) return parts[0].transform.parent;

        Transform commonParent = parts[0].transform.parent;
        
        for (int i = 1; i < parts.Length; i++)
        {
            commonParent = FindCommonAncestor(commonParent, parts[i].transform.parent);
            if (commonParent == null) break;
        }

        return commonParent;
    }

    /// <summary>
    /// 查找两个Transform的共同祖先
    /// </summary>
    private Transform FindCommonAncestor(Transform a, Transform b)
    {
        if (a == null || b == null) return null;

        // 获取a的所有祖先
        var ancestorsA = new System.Collections.Generic.List<Transform>();
        Transform current = a;
        while (current != null)
        {
            ancestorsA.Add(current);
            current = current.parent;
        }

        // 检查b的祖先是否在a的祖先列表中
        current = b;
        while (current != null)
        {
            if (ancestorsA.Contains(current))
            {
                return current;
            }
            current = current.parent;
        }

        return null;
    }

    /// <summary>
    /// 计算最佳装配观看位置（基于摄像机坐标系的相对位置）
    /// </summary>
    public Vector3 CalculateOptimalAssemblyPosition()
    {
        if (vrCamera == null)
        {
            Debug.LogError("[VRAssemblyPositioner] VR摄像机未设置");
            return Vector3.zero;
        }

        Vector3 cameraPosition = vrCamera.position;
        Vector3 cameraForward = vrCamera.forward;
        Vector3 cameraRight = vrCamera.right;
        Vector3 cameraUp = vrCamera.up;

        Debug.Log("=== 计算装配区域最佳位置 ===");
        Debug.Log($"[VRAssemblyPositioner] 📷 摄像机位置: {cameraPosition}");
        Debug.Log($"[VRAssemblyPositioner] 📷 摄像机前方向: {cameraForward}");
        Debug.Log($"[VRAssemblyPositioner] 📷 摄像机右方向: {cameraRight}");
        Debug.Log($"[VRAssemblyPositioner] 📷 摄像机上方向: {cameraUp}");

        // 基于摄像机坐标系的相对位置计算
        Debug.Log($"[VRAssemblyPositioner] 🎯 相对位置设置: {relativePosition}");

        Vector3 optimalPosition = cameraPosition +
                                 cameraRight * relativePosition.x +      // 左右偏移
                                 cameraUp * relativePosition.y +         // 上下偏移
                                 cameraForward * relativePosition.z;     // 前后偏移

        Debug.Log($"[VRAssemblyPositioner] 📊 位置分解:");
        Debug.Log($"   基础位置: {cameraPosition}");
        Debug.Log($"   右偏移: {cameraRight * relativePosition.x} (X: {relativePosition.x})");
        Debug.Log($"   上偏移: {cameraUp * relativePosition.y} (Y: {relativePosition.y})");
        Debug.Log($"   前偏移: {cameraForward * relativePosition.z} (Z: {relativePosition.z})");

        Debug.Log($"[VRAssemblyPositioner] 📍 计算的目标位置: {optimalPosition}");
        Debug.Log($"[VRAssemblyPositioner] 📏 距离摄像机: {Vector3.Distance(cameraPosition, optimalPosition):F2}m");

        // 计算相对于摄像机的位置信息
        Vector3 relativeToCamera = optimalPosition - cameraPosition;
        float forwardDistance = Vector3.Dot(relativeToCamera, cameraForward);
        float rightDistance = Vector3.Dot(relativeToCamera, cameraRight);
        float upDistance = Vector3.Dot(relativeToCamera, cameraUp);

        Debug.Log($"[VRAssemblyPositioner] 📐 相对摄像机位置:");
        Debug.Log($"   前方距离: {forwardDistance:F2}m");
        Debug.Log($"   右侧距离: {rightDistance:F2}m (负值=左侧)");
        Debug.Log($"   上方距离: {upDistance:F2}m (负值=下方)");
        Debug.Log("=== 位置计算完成 ===");

        return optimalPosition;
    }

    /// <summary>
    /// 基于装配点计算最佳位置和朝向（使用简化朝向计算）
    /// </summary>
    public (Vector3 position, Quaternion rotation) CalculateOptimalPositionForAssemblyPoint(
        AssemblyPart movingPart, AssemblyPart targetPart, int movingRefIndex = 0, int targetRefIndex = 0)
    {
        // === 详细诊断信息 ===
        Debug.Log("=== VR装配位置计算开始 ===");

        // 检查VR摄像机
        if (vrCamera == null)
        {
            Debug.LogError("[VRAssemblyPositioner] ❌ VR摄像机未设置，尝试自动查找...");
            vrCamera = Camera.main?.transform;
            if (vrCamera != null)
            {
                Debug.Log($"[VRAssemblyPositioner] ✅ 自动找到摄像机: {vrCamera.name}");
            }
            else
            {
                Debug.LogError("[VRAssemblyPositioner] ❌ 无法找到任何摄像机");
                return (Vector3.zero, Quaternion.identity);
            }
        }

        // 检查目标零件
        if (targetPart == null)
        {
            Debug.LogError("[VRAssemblyPositioner] ❌ 目标零件未设置");
            return (Vector3.zero, Quaternion.identity);
        }

        Vector3 cameraPosition = vrCamera.position;
        Vector3 cameraForward = vrCamera.forward;

        Debug.Log($"[VRAssemblyPositioner] 📷 摄像机位置: {cameraPosition}");
        Debug.Log($"[VRAssemblyPositioner] 📷 摄像机前方向: {cameraForward}");
        Debug.Log($"[VRAssemblyPositioner] 🎯 目标零件: {targetPart.PartName}");

        // === 第一步：使用高级定位系统计算最佳观看位置 ===
        Vector3 targetRootPosition = CalculateOptimalAssemblyPosition();

        Debug.Log($"[VRAssemblyPositioner] 📍 计算的目标位置: {targetRootPosition}");
        Debug.Log($"[VRAssemblyPositioner] 📏 距离摄像机: {Vector3.Distance(cameraPosition, targetRootPosition):F2}m");

        // === 第二步：简化朝向计算 - 让装配点Z轴朝向摄像机 ===
        Transform targetRefPoint = targetPart.GetReferencePoint(targetRefIndex);
        if (targetRefPoint == null)
        {
            Debug.LogError($"[VRAssemblyPositioner] ❌ 无法获取目标零件的参考点 {targetRefIndex}");
            return (targetRootPosition, Quaternion.identity);
        }

        Debug.Log($"[VRAssemblyPositioner] 📌 目标参考点: {targetRefPoint.name}, 位置: {targetRefPoint.position}");

        // 使用简化的朝向计算方法
        Quaternion targetRotation = CalculateSimpleOrientationToCamera(targetRefPoint, vrCamera);

        Debug.Log($"[VRAssemblyPositioner] 🔄 计算的目标朝向: {targetRotation.eulerAngles}");
        Debug.Log("=== VR装配位置计算完成 ===");

        return (targetRootPosition, targetRotation);
    }



    /// <summary>
    /// 计算装配区域面向摄像机的朝向
    /// </summary>
    public Quaternion CalculateOptimalAssemblyRotation(Vector3 assemblyPosition)
    {
        if (vrCamera == null) return Quaternion.identity;

        Vector3 cameraPosition = vrCamera.position;
        Vector3 toCamera = (cameraPosition - assemblyPosition).normalized;

        // 让装配区域面向用户
        return Quaternion.LookRotation(-toCamera, Vector3.up);
    }

    /// <summary>
    /// 简化的朝向计算：让装配点Z轴朝向摄像机（详细调试版本）
    /// </summary>
    public Quaternion CalculateSimpleOrientationToCamera(Transform referencePoint, Transform camera)
    {
        if (referencePoint == null || camera == null)
        {
            Debug.LogError("[VRAssemblyPositioner] ❌ 参考点或摄像机为空");
            return Quaternion.identity;
        }

        Debug.Log("=== 简化朝向计算开始 ===");
        Debug.Log($"[VRAssemblyPositioner] 🎯 参考点: {referencePoint.name}");
        Debug.Log($"[VRAssemblyPositioner] 📷 摄像机: {camera.name}");

        Vector3 refPointPosition = referencePoint.position;
        Vector3 cameraPosition = camera.position;
        float distance = Vector3.Distance(refPointPosition, cameraPosition);

        // 计算从装配点到摄像机的方向
        Vector3 toCameraDirection = (cameraPosition - refPointPosition).normalized;

        Debug.Log($"[VRAssemblyPositioner] 📍 位置信息:");
        Debug.Log($"   参考点位置: {refPointPosition}");
        Debug.Log($"   摄像机位置: {cameraPosition}");
        Debug.Log($"   距离: {distance:F2}m");
        Debug.Log($"   到摄像机方向: {toCameraDirection}");

        // 显示参考点当前状态
        Debug.Log($"[VRAssemblyPositioner] 🧭 参考点当前状态:");
        Debug.Log($"   当前旋转: {referencePoint.rotation.eulerAngles}");
        Debug.Log($"   当前Forward(+Z): {referencePoint.forward}");
        Debug.Log($"   当前Back(-Z): {-referencePoint.forward}");
        Debug.Log($"   当前Up(+Y): {referencePoint.up}");
        Debug.Log($"   当前Right(+X): {referencePoint.right}");

        // 分析当前朝向与目标的关系
        float forwardDot = Vector3.Dot(referencePoint.forward, toCameraDirection);
        float backDot = Vector3.Dot(-referencePoint.forward, toCameraDirection);
        float upDot = Vector3.Dot(referencePoint.up, toCameraDirection);
        float rightDot = Vector3.Dot(referencePoint.right, toCameraDirection);

        Debug.Log($"[VRAssemblyPositioner] 📊 当前各轴与摄像机方向的对齐度:");
        Debug.Log($"   Forward(+Z): {forwardDot:F3}");
        Debug.Log($"   Back(-Z): {backDot:F3}");
        Debug.Log($"   Up(+Y): {upDot:F3}");
        Debug.Log($"   Right(+X): {rightDot:F3}");

        // 让装配点的Z轴朝向摄像机
        // LookRotation的第一个参数是forward方向，即Z轴方向
        // 我们希望Z轴朝向摄像机，所以直接使用toCameraDirection
        Quaternion targetRotation = Quaternion.LookRotation(toCameraDirection, Vector3.up);

        Debug.Log($"[VRAssemblyPositioner] 🎯 朝向计算详情:");
        Debug.Log($"   到摄像机方向: {toCameraDirection}");
        Debug.Log($"   期望Z轴方向: {toCameraDirection} (应该朝向摄像机)");
        Debug.Log($"   期望-Z轴方向: {-toCameraDirection} (装配面，应该背离摄像机)");

        Debug.Log($"[VRAssemblyPositioner] 🔄 旋转计算:");
        Debug.Log($"   LookRotation输入 - forward: {toCameraDirection}, up: {Vector3.up}");
        Debug.Log($"   计算的旋转: {targetRotation.eulerAngles}");

        // 验证计算结果
        Vector3 newForward = targetRotation * Vector3.forward;
        Vector3 newBack = targetRotation * Vector3.back;
        Vector3 newUp = targetRotation * Vector3.up;
        Vector3 newRight = targetRotation * Vector3.right;

        Debug.Log($"[VRAssemblyPositioner] ✅ 计算结果验证:");
        Debug.Log($"   新的Forward(+Z): {newForward}");
        Debug.Log($"   新的Back(-Z): {newBack}");
        Debug.Log($"   新的Up(+Y): {newUp}");
        Debug.Log($"   新的Right(+X): {newRight}");

        // 验证对齐度
        float newForwardAlignment = Vector3.Dot(newForward, toCameraDirection);
        float newBackAlignment = Vector3.Dot(newBack, toCameraDirection);

        Debug.Log($"[VRAssemblyPositioner] 📈 对齐度验证:");
        Debug.Log($"   新Forward与摄像机方向对齐度: {newForwardAlignment:F3} (期望接近1.0)");
        Debug.Log($"   新Back与摄像机方向对齐度: {newBackAlignment:F3} (期望接近-1.0)");

        if (newForwardAlignment > 0.95f)
        {
            Debug.Log($"   ✅ Z轴朝向摄像机成功！");
        }
        else
        {
            Debug.LogWarning($"   ⚠️ Z轴朝向摄像机可能有偏差");
        }

        if (newBackAlignment < -0.95f)
        {
            Debug.Log($"   ✅ 装配面(-Z)朝向用户成功！");
        }
        else
        {
            Debug.LogWarning($"   ⚠️ 装配面(-Z)朝向用户可能有偏差");
        }

        // 计算需要的旋转角度
        float rotationAngle = Quaternion.Angle(referencePoint.rotation, targetRotation);
        Debug.Log($"[VRAssemblyPositioner] 📐 需要旋转角度: {rotationAngle:F1}°");

        Debug.Log("=== 简化朝向计算完成 ===");

        return targetRotation;
    }

    /// <summary>
    /// 基于Test.cs方法：通过旋转装配区域使安装点朝向摄像机
    /// </summary>
    /// <param name="targetPart">目标零件</param>
    /// <param name="referencePointIndex">参考点索引</param>
    /// <param name="camera">摄像机</param>
    /// <param name="useNegativeZ">是否让-Z轴朝向摄像机</param>
    public IEnumerator AlignMountPointToCameraByRotatingAssemblyRoot(AssemblyPart targetPart, int referencePointIndex, Transform camera, bool useNegativeZ = true)
    {
        if (targetPart == null || camera == null || assemblyRoot == null)
        {
            Debug.LogError("[VRAssemblyPositioner] ❌ 目标零件、摄像机或装配根节点为空");
            yield break;
        }

        Transform mountPoint = targetPart.GetReferencePoint(referencePointIndex);
        if (mountPoint == null)
        {
            Debug.LogError($"[VRAssemblyPositioner] ❌ 无法获取参考点 {referencePointIndex}");
            yield break;
        }

        Debug.Log("=== 基于Test.cs方法：通过旋转装配区域使安装点朝向摄像机 ===");
        Debug.Log($"[VRAssemblyPositioner] 🎯 目标零件: {targetPart.PartName}");
        Debug.Log($"[VRAssemblyPositioner] 📍 安装点: {mountPoint.name}");
        Debug.Log($"[VRAssemblyPositioner] 🏗️ 装配根节点: {assemblyRoot.name}");

        // 记录旋转前状态
        Vector3 beforeAssemblyRootRotation = assemblyRoot.rotation.eulerAngles;
        Vector3 beforeMountPointForward = mountPoint.forward;

        Debug.Log($"[VRAssemblyPositioner] 🔍 旋转前状态:");
        Debug.Log($"   装配根节点旋转: {beforeAssemblyRootRotation}");
        Debug.Log($"   安装点Z轴方向: {beforeMountPointForward}");

        // === 完全按照Test.cs的逻辑实现 ===

        // 1. 计算目标方向（从安装点到摄像机的方向）
        Vector3 cameraPos = camera.position;
        Vector3 mountPointPos = mountPoint.position;
        Vector3 targetDirection = (cameraPos - mountPointPos).normalized;

        Debug.Log($"[VRAssemblyPositioner] 📷 目标方向（到摄像机）: {targetDirection}");

        // 2. 确定要对齐的轴（对应Test.cs的mountPointAxis）
        Vector3 mountPointAxis = useNegativeZ ? -Vector3.forward : Vector3.forward;
        string axisName = useNegativeZ ? "-Z轴" : "Z轴";

        Debug.Log($"[VRAssemblyPositioner] 🎯 对齐轴: {axisName} ({mountPointAxis})");

        // 3. 获取安装点当前的世界坐标系中的指定轴方向（对应Test.cs的currentWorldAxis）
        Vector3 currentWorldAxis = mountPoint.TransformDirection(mountPointAxis.normalized);

        Debug.Log($"[VRAssemblyPositioner] 🧭 当前世界轴方向: {currentWorldAxis}");

        // 4. 计算从当前轴方向到目标方向的旋转（对应Test.cs的rotationToTarget）
        Quaternion rotationToTarget = Quaternion.FromToRotation(currentWorldAxis, targetDirection);

        Debug.Log($"[VRAssemblyPositioner] 🔄 需要的旋转: {rotationToTarget.eulerAngles}");

        // 5. 获取装配根节点当前的世界旋转（对应Test.cs的parentWorldRotation）
        Quaternion assemblyRootWorldRotation = assemblyRoot.rotation;

        // 6. 计算装配根节点的目标世界旋转（对应Test.cs的核心公式）
        Quaternion targetRotation = rotationToTarget * assemblyRootWorldRotation;

        Debug.Log($"[VRAssemblyPositioner] 🎯 装配根节点目标旋转: {targetRotation.eulerAngles}");

        // 7. 平滑旋转装配根节点（对应Test.cs的平滑动画，但旋转的是装配根节点）
        Debug.Log($"[VRAssemblyPositioner] 🔄 开始平滑旋转装配根节点...");

        float rotationSpeed = 5f; // 旋转速度
        bool isRotating = true;

        while (isRotating)
        {
            // 使用 Slerp 平滑插值旋转装配根节点
            assemblyRoot.rotation = Quaternion.Slerp(assemblyRoot.rotation, targetRotation, Time.deltaTime * rotationSpeed);

            // 检查是否接近目标旋转
            if (Quaternion.Angle(assemblyRoot.rotation, targetRotation) < 0.1f)
            {
                assemblyRoot.rotation = targetRotation; // 确保精确到达
                isRotating = false; // 停止旋转
            }

            yield return null; // 等待下一帧
        }

        // 8. 验证旋转结果
        Vector3 afterMountPointForward = mountPoint.forward;
        float zAxisAlignment = Vector3.Dot(afterMountPointForward, targetDirection);
        float negZAxisAlignment = Vector3.Dot(-afterMountPointForward, targetDirection);

        Debug.Log($"[VRAssemblyPositioner] 📊 旋转后验证:");
        Debug.Log($"   装配根节点新旋转: {assemblyRoot.rotation.eulerAngles}");
        Debug.Log($"   安装点新Z轴方向: {afterMountPointForward}");
        Debug.Log($"   Z轴对齐度: {zAxisAlignment:F3}");
        Debug.Log($"   -Z轴对齐度: {negZAxisAlignment:F3}");

        // 9. 判断成功与否
        float finalAlignment = useNegativeZ ? negZAxisAlignment : zAxisAlignment;

        if (finalAlignment > 0.95f)
        {
            Debug.Log($"[VRAssemblyPositioner] ✅ 通过旋转装配区域使安装点朝向摄像机成功！{axisName}对齐度: {finalAlignment:F3}");
        }
        else if (finalAlignment > 0.8f)
        {
            Debug.Log($"[VRAssemblyPositioner] ⚠️ 通过旋转装配区域使安装点朝向摄像机基本成功，{axisName}对齐度: {finalAlignment:F3}");
        }
        else
        {
            Debug.Log($"[VRAssemblyPositioner] ❌ 通过旋转装配区域使安装点朝向摄像机失败，{axisName}对齐度: {finalAlignment:F3}");
        }

        Debug.Log("=== 通过旋转装配区域使安装点朝向摄像机完成 ===");
    }

    // ==================== 实际应用场景的集成接口 ====================

    /// <summary>
    /// 第一部分功能的公共接口：用户主动调整视角
    /// 适用于用户按键触发的场景
    /// </summary>
    public IEnumerator AdjustViewForUser()
    {
        Debug.Log("=== 用户主动调整视角 ===");
        Debug.Log("将装配区域移动到摄像机视野的最佳位置");

        yield return MoveAssemblyToOptimalPosition();

        Debug.Log("=== 用户视角调整完成 ===");
    }

    /// <summary>
    /// 第二部分功能的公共接口：装配步骤前自动调整朝向
    /// 适用于播放装配动画前的自动调整
    /// </summary>
    /// <param name="targetPartName">目标零件名称</param>
    /// <param name="referencePointIndex">参考点索引</param>
    /// <param name="useNegativeZ">是否使用-Z轴朝向摄像机</param>
    public IEnumerator AdjustOrientationForAssemblyStep(string targetPartName, int referencePointIndex, bool useNegativeZ = true)
    {
        Debug.Log("=== 装配步骤前自动调整朝向 ===");
        Debug.Log($"目标零件: {targetPartName}");
        Debug.Log($"参考点索引: {referencePointIndex}");
        Debug.Log($"使用{(useNegativeZ ? "-Z轴(装配面)" : "Z轴(装配点背面)")}朝向摄像机");

        // 查找目标零件
        AssemblyPart targetPart = FindAssemblyPartByName(targetPartName);
        if (targetPart == null)
        {
            Debug.LogError($"[VRAssemblyPositioner] ❌ 无法找到零件: {targetPartName}");
            yield break;
        }

        // 验证参考点索引
        if (referencePointIndex >= targetPart.ReferencePoints.Length)
        {
            Debug.LogError($"[VRAssemblyPositioner] ❌ 参考点索引 {referencePointIndex} 超出范围！零件 {targetPartName} 只有 {targetPart.ReferencePoints.Length} 个参考点");
            yield break;
        }

        // 获取摄像机
        Transform camera = GetCurrentCamera();
        if (camera == null)
        {
            Debug.LogError("[VRAssemblyPositioner] ❌ 无法获取当前摄像机");
            yield break;
        }

        // 执行朝向调整
        yield return AlignMountPointToCameraByRotatingAssemblyRoot(targetPart, referencePointIndex, camera, useNegativeZ);

        Debug.Log("=== 装配步骤朝向调整完成 ===");
    }

    /// <summary>
    /// 根据名称查找装配零件
    /// </summary>
    private AssemblyPart FindAssemblyPartByName(string partName)
    {
        if (assemblyRoot == null) return null;

        // 在装配根节点下查找所有AssemblyPart组件
        AssemblyPart[] allParts = assemblyRoot.GetComponentsInChildren<AssemblyPart>();

        foreach (var part in allParts)
        {
            if (part.PartName == partName)
            {
                return part;
            }
        }

        Debug.LogWarning($"[VRAssemblyPositioner] 未找到名为 '{partName}' 的零件");
        return null;
    }

    /// <summary>
    /// 获取当前摄像机
    /// </summary>
    private Transform GetCurrentCamera()
    {
        // 优先使用VR摄像机
        if (vrCamera != null)
        {
            return vrCamera;
        }

        // 回退到主摄像机
        Camera mainCamera = Camera.main;
        if (mainCamera != null)
        {
            return mainCamera.transform;
        }

        Debug.LogError("[VRAssemblyPositioner] 无法找到可用的摄像机");
        return null;
    }

    /// <summary>
    /// 可配置的朝向计算：让装配点的指定轴朝向摄像机
    /// </summary>
    /// <param name="referencePoint">参考点</param>
    /// <param name="camera">摄像机</param>
    /// <param name="useNegativeZ">是否让-Z轴朝向摄像机（true=装配面朝向用户，false=装配点背面朝向用户）</param>
    public Quaternion CalculateConfigurableOrientationToCamera(Transform referencePoint, Transform camera, bool useNegativeZ = true)
    {
        if (referencePoint == null || camera == null)
        {
            Debug.LogError("[VRAssemblyPositioner] ❌ 参考点或摄像机为空");
            return Quaternion.identity;
        }

        Debug.Log("=== 可配置朝向计算开始 ===");
        Debug.Log($"[VRAssemblyPositioner] 🎯 参考点: {referencePoint.name}");
        Debug.Log($"[VRAssemblyPositioner] 📷 摄像机: {camera.name}");
        Debug.Log($"[VRAssemblyPositioner] ⚙️ 使用-Z轴朝向摄像机: {useNegativeZ}");

        Vector3 refPointPosition = referencePoint.position;
        Vector3 cameraPosition = camera.position;

        // 计算从装配点到摄像机的方向
        Vector3 toCameraDirection = (cameraPosition - refPointPosition).normalized;

        Debug.Log($"[VRAssemblyPositioner] 📍 位置信息:");
        Debug.Log($"   参考点位置: {refPointPosition}");
        Debug.Log($"   摄像机位置: {cameraPosition}");
        Debug.Log($"   到摄像机方向: {toCameraDirection}");

        Quaternion targetRotation;

        if (useNegativeZ)
        {
            // 让-Z轴朝向摄像机（装配面朝向用户）
            // LookRotation的forward参数是Z轴方向，我们希望-Z朝向摄像机
            // 所以Z轴应该朝向摄像机的反方向
            targetRotation = Quaternion.LookRotation(-toCameraDirection, Vector3.up);
            Debug.Log($"[VRAssemblyPositioner] 🔄 配置: -Z轴朝向摄像机");
            Debug.Log($"   Z轴方向: {-toCameraDirection} (背离摄像机)");
            Debug.Log($"   -Z轴方向: {toCameraDirection} (朝向摄像机)");
        }
        else
        {
            // 让Z轴朝向摄像机（装配点背面朝向用户）
            targetRotation = Quaternion.LookRotation(toCameraDirection, Vector3.up);
            Debug.Log($"[VRAssemblyPositioner] 🔄 配置: Z轴朝向摄像机");
            Debug.Log($"   Z轴方向: {toCameraDirection} (朝向摄像机)");
            Debug.Log($"   -Z轴方向: {-toCameraDirection} (背离摄像机)");
        }

        Debug.Log($"[VRAssemblyPositioner] 🔄 计算的旋转: {targetRotation.eulerAngles}");

        // 验证计算结果
        Vector3 newForward = targetRotation * Vector3.forward;
        Vector3 newBack = targetRotation * Vector3.back;

        Debug.Log($"[VRAssemblyPositioner] ✅ 计算结果验证:");
        Debug.Log($"   新的Z轴方向: {newForward}");
        Debug.Log($"   新的-Z轴方向: {newBack}");

        // 验证对齐度
        float zAxisAlignment = Vector3.Dot(newForward, toCameraDirection);
        float negZAxisAlignment = Vector3.Dot(newBack, toCameraDirection);

        Debug.Log($"[VRAssemblyPositioner] 📈 对齐度验证:");
        Debug.Log($"   Z轴与摄像机方向对齐度: {zAxisAlignment:F3}");
        Debug.Log($"   -Z轴与摄像机方向对齐度: {negZAxisAlignment:F3}");

        if (useNegativeZ)
        {
            if (negZAxisAlignment > 0.95f)
            {
                Debug.Log($"   ✅ -Z轴朝向摄像机成功！");
            }
            else
            {
                Debug.LogWarning($"   ⚠️ -Z轴朝向摄像机可能有偏差");
            }
        }
        else
        {
            if (zAxisAlignment > 0.95f)
            {
                Debug.Log($"   ✅ Z轴朝向摄像机成功！");
            }
            else
            {
                Debug.LogWarning($"   ⚠️ Z轴朝向摄像机可能有偏差");
            }
        }

        Debug.Log("=== 可配置朝向计算完成 ===");

        return targetRotation;
    }

    /// <summary>
    /// 将装配区域移动到摄像机视野的固定位置
    /// </summary>
    public IEnumerator MoveAssemblyToOptimalPosition()
    {
        if (assemblyRoot == null || isRepositioning)
        {
            Debug.LogWarning("[VRAssemblyPositioner] 装配根节点未设置或正在重新定位");
            yield break;
        }

        if (vrCamera == null)
        {
            Debug.LogError("[VRAssemblyPositioner] VR摄像机未设置，无法计算目标位置");
            yield break;
        }

        isRepositioning = true;

        // 实时计算基于当前摄像机位置的目标位置
        Vector3 targetPosition = CalculateOptimalAssemblyPosition();

        // 计算朝向摄像机的旋转（让装配区域面向用户）
        Vector3 toCamera = (vrCamera.position - targetPosition).normalized;
        Quaternion targetRotation = Quaternion.LookRotation(-toCamera, Vector3.up);

        Vector3 startPosition = assemblyRoot.position;
        Quaternion startRotation = assemblyRoot.rotation;

        Debug.Log("=== 开始移动装配区域到摄像机视野固定位置 ===");
        Debug.Log($"[VRAssemblyPositioner] 📍 起始位置: {startPosition}");
        Debug.Log($"[VRAssemblyPositioner] 📍 目标位置: {targetPosition}");
        Debug.Log($"[VRAssemblyPositioner] 📏 移动距离: {Vector3.Distance(startPosition, targetPosition):F2}m");
        Debug.Log($"[VRAssemblyPositioner] 🔄 起始旋转: {startRotation.eulerAngles}");
        Debug.Log($"[VRAssemblyPositioner] 🔄 目标旋转: {targetRotation.eulerAngles}");

        float elapsed = 0f;
        while (elapsed < repositionDuration)
        {
            elapsed += Time.deltaTime;
            float t = Mathf.SmoothStep(0f, 1f, elapsed / repositionDuration);

            // 平滑移动位置和旋转
            assemblyRoot.position = Vector3.Lerp(startPosition, targetPosition, t);
            assemblyRoot.rotation = Quaternion.Slerp(startRotation, targetRotation, t);

            yield return null;
        }

        // 确保最终位置准确
        assemblyRoot.position = targetPosition;
        assemblyRoot.rotation = targetRotation;

        isRepositioning = false;

        Debug.Log($"[VRAssemblyPositioner] ✅ 装配区域移动完成");
        Debug.Log($"[VRAssemblyPositioner] 📍 最终位置: {assemblyRoot.position}");
        Debug.Log($"[VRAssemblyPositioner] 🔄 最终旋转: {assemblyRoot.rotation.eulerAngles}");
        Debug.Log("=== 移动完成 ===");
    }

    /// <summary>
    /// 根据装配步骤调整最佳位置和朝向（智能定位）
    /// </summary>
    public IEnumerator AdjustOrientationForAssemblyStep(AssemblyPart movingPart, AssemblyPart targetPart)
    {
        if (assemblyRoot == null || isRepositioning) yield break;

        Debug.Log($"[VRAssemblyPositioner] 开始智能定位装配步骤: {movingPart?.PartName} -> {targetPart?.PartName}");

        // 使用新的智能定位方法
        var (targetPosition, targetRotation) = CalculateOptimalPositionForAssemblyPoint(movingPart, targetPart, 0, 0);

        // 执行位置和朝向调整
        yield return MoveAssemblyToPosition(targetPosition, targetRotation);

        Debug.Log("[VRAssemblyPositioner] 智能定位完成");
    }

    /// <summary>
    /// 根据装配步骤调整最佳朝向（保留原方法，用于简单朝向调整）
    /// </summary>
    public IEnumerator AdjustOrientationForAssemblyStepSimple(AssemblyPart movingPart, AssemblyPart targetPart)
    {
        if (assemblyRoot == null || isRepositioning) yield break;

        // 获取装配点位置
        Vector3 assemblyPoint = targetPart.GetReferencePoint(0).position;
        Vector3 cameraPosition = vrCamera.position;

        // 计算从装配点到摄像机的方向
        Vector3 toCamera = (cameraPosition - assemblyPoint).normalized;

        // 调整装配体朝向，使装配面朝向用户
        Quaternion targetRotation = Quaternion.LookRotation(-toCamera, Vector3.up);

        yield return RotateAssemblyToOrientation(targetRotation, repositionDuration * 0.5f);
    }

    /// <summary>
    /// 旋转装配区域到指定朝向
    /// </summary>
    public IEnumerator RotateAssemblyToOrientation(Quaternion targetRotation, float duration)
    {
        if (assemblyRoot == null || isRepositioning) yield break;

        isRepositioning = true;

        Quaternion startRotation = assemblyRoot.rotation;
        float elapsed = 0f;

        while (elapsed < duration)
        {
            elapsed += Time.deltaTime;
            float t = Mathf.SmoothStep(0f, 1f, elapsed / duration);

            assemblyRoot.rotation = Quaternion.Slerp(startRotation, targetRotation, t);
            yield return null;
        }

        assemblyRoot.rotation = targetRotation;
        isRepositioning = false;
    }

    /// <summary>
    /// 恢复装配区域到原始位置
    /// </summary>
    public IEnumerator RestoreOriginalPosition()
    {
        if (assemblyRoot == null) yield break;

        yield return StartCoroutine(MoveAssemblyToPosition(originalAssemblyPosition, originalAssemblyRotation));
    }

    /// <summary>
    /// 移动装配区域到指定位置（修复旋转偏移问题）
    /// </summary>
    public IEnumerator MoveAssemblyToPosition(Vector3 targetPosition, Quaternion targetRotation)
    {
        if (assemblyRoot == null || isRepositioning) yield break;

        isRepositioning = true;

        Vector3 startPosition = assemblyRoot.position;
        Quaternion startRotation = assemblyRoot.rotation;

        Debug.Log($"[VRAssemblyPositioner] 开始移动: 从 {startPosition} 到 {targetPosition}");
        Debug.Log($"[VRAssemblyPositioner] 开始旋转: 从 {startRotation.eulerAngles} 到 {targetRotation.eulerAngles}");

        float elapsed = 0f;
        while (elapsed < repositionDuration)
        {
            elapsed += Time.deltaTime;
            float t = Mathf.SmoothStep(0f, 1f, elapsed / repositionDuration);

            // 先设置旋转，再设置位置（避免旋转导致的位置偏移）
            assemblyRoot.rotation = Quaternion.Slerp(startRotation, targetRotation, t);
            assemblyRoot.position = Vector3.Lerp(startPosition, targetPosition, t);

            yield return null;
        }

        // 确保最终状态准确
        assemblyRoot.rotation = targetRotation;
        assemblyRoot.position = targetPosition;

        Debug.Log($"[VRAssemblyPositioner] 移动完成: 最终位置 {assemblyRoot.position}");
        Debug.Log($"[VRAssemblyPositioner] 旋转完成: 最终朝向 {assemblyRoot.rotation.eulerAngles}");

        isRepositioning = false;
    }

    /// <summary>
    /// 公共方法：开始装配时调用
    /// </summary>
    public void OnAssemblyStart()
    {
        StartCoroutine(MoveAssemblyToOptimalPosition());
    }

    /// <summary>
    /// 公共方法：装配步骤开始时调用
    /// </summary>
    public void OnAssemblyStepStart(AssemblyPart movingPart, AssemblyPart targetPart)
    {
        StartCoroutine(AdjustOrientationForAssemblyStep(movingPart, targetPart));
    }

    /// <summary>
    /// 公共方法：装配结束时调用
    /// </summary>
    public void OnAssemblyEnd()
    {
        StartCoroutine(RestoreOriginalPosition());
    }

    // 调试用的Gizmos绘制
    void OnDrawGizmos()
    {
        if (!showDebugGizmos || vrCamera == null) return;

        // 绘制最佳观看位置
        Vector3 optimalPos = CalculateOptimalAssemblyPosition();
        Gizmos.color = optimalPositionColor;
        Gizmos.DrawWireSphere(optimalPos, 0.2f);

        // 绘制从摄像机到最佳位置的连线
        Gizmos.color = Color.yellow;
        Gizmos.DrawLine(vrCamera.position, optimalPos);
    }
}
