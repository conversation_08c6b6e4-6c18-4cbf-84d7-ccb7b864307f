using UnityEngine;
using System;

// 条件编译：只在安装了XR Interaction Toolkit时编译XR相关代码
#if UNITY_XR_INTERACTION_TOOLKIT
using UnityEngine.XR.Interaction.Toolkit;
using UnityEngine.XR;
#endif

/// <summary>
/// VR输入管理器
/// 处理VR控制器输入，替代传统的鼠标键盘输入
/// </summary>
public class VRInputManager : MonoBehaviour
{
#if UNITY_XR_INTERACTION_TOOLKIT
    [Header("VR控制器")]
    [SerializeField] private XRRayInteractor leftRayInteractor;
    [SerializeField] private XRRayInteractor rightRayInteractor;
    [SerializeField] private XRController leftController;
    [SerializeField] private XRController rightController;
#endif

    [Header("输入设置")]
    [SerializeField] private bool useLeftController = true;
    [SerializeField] private bool useRightController = true;
    [SerializeField] private LayerMask interactionLayerMask = -1;

    [Header("反馈设置")]
    [SerializeField] private bool enableHapticFeedback = true;
    [SerializeField] private float hapticIntensity = 0.5f;
    [SerializeField] private float hapticDuration = 0.1f;

    // 事件
    public event Action<AssemblyPart> OnPartSelected;
    public event Action OnNextStepRequested;
    public event Action OnResetRequested;
    public event Action OnReplayRequested;

    // 当前选中的零件
    private AssemblyPart currentSelectedPart;
#if UNITY_XR_INTERACTION_TOOLKIT
    private XRRayInteractor activeInteractor;
#endif

    // 输入状态
    private bool isSelectPressed = false;
    private bool isMenuPressed = false;

    void Start()
    {
#if UNITY_XR_INTERACTION_TOOLKIT
        InitializeVRInput();
#else
        Debug.LogWarning("[VRInputManager] XR Interaction Toolkit未安装，VR输入功能将被禁用");
        enabled = false;
#endif
    }

    void Update()
    {
#if UNITY_XR_INTERACTION_TOOLKIT
        HandleVRInput();
#else
        // 在没有XR包时，使用传统输入作为备用
        HandleFallbackInput();
#endif
    }

    /// <summary>
    /// 初始化VR输入系统
    /// </summary>
    private void InitializeVRInput()
    {
#if UNITY_XR_INTERACTION_TOOLKIT
        // 自动查找VR控制器组件
        if (leftRayInteractor == null)
        {
            var leftControllerGO = GameObject.Find("LeftHand Controller");
            if (leftControllerGO != null)
            {
                leftRayInteractor = leftControllerGO.GetComponent<XRRayInteractor>();
                leftController = leftControllerGO.GetComponent<XRController>();
            }
        }

        if (rightRayInteractor == null)
        {
            var rightControllerGO = GameObject.Find("RightHand Controller");
            if (rightControllerGO != null)
            {
                rightRayInteractor = rightControllerGO.GetComponent<XRRayInteractor>();
                rightController = rightControllerGO.GetComponent<XRController>();
            }
        }

        // 验证组件
        if (leftRayInteractor == null && rightRayInteractor == null)
        {
            Debug.LogError("[VRInputManager] 未找到VR控制器！请确保场景中有XR Origin和控制器。");
            enabled = false;
            return;
        }

        Debug.Log("[VRInputManager] VR输入系统初始化完成");
#endif
    }

    /// <summary>
    /// 处理VR输入
    /// </summary>
    private void HandleVRInput()
    {
#if UNITY_XR_INTERACTION_TOOLKIT
        // 处理左控制器输入
        if (useLeftController && leftController != null)
        {
            HandleControllerInput(leftController, leftRayInteractor);
        }

        // 处理右控制器输入
        if (useRightController && rightController != null)
        {
            HandleControllerInput(rightController, rightRayInteractor);
        }
#endif
    }

    /// <summary>
    /// 备用输入处理（当XR包未安装时）
    /// </summary>
    private void HandleFallbackInput()
    {
        // 使用鼠标点击作为备用输入
        if (Input.GetMouseButtonDown(0))
        {
            Ray ray = Camera.main.ScreenPointToRay(Input.mousePosition);
            if (Physics.Raycast(ray, out RaycastHit hit))
            {
                AssemblyPart part = hit.collider.GetComponent<AssemblyPart>();
                if (part == null)
                {
                    part = hit.collider.GetComponentInParent<AssemblyPart>();
                }

                if (part != null)
                {
                    SelectPart(part, null);
                }
            }
        }

        // 使用键盘作为备用输入
        if (Input.GetKeyDown(KeyCode.Space))
        {
            OnNextStepRequested?.Invoke();
        }

        if (Input.GetKeyDown(KeyCode.R))
        {
            OnResetRequested?.Invoke();
        }

        if (Input.GetKeyDown(KeyCode.P))
        {
            OnReplayRequested?.Invoke();
        }
    }

    /// <summary>
    /// 处理单个控制器的输入
    /// </summary>
    private void HandleControllerInput(object controller, object rayInteractor)
    {
#if UNITY_XR_INTERACTION_TOOLKIT
        XRController xrController = controller as XRController;
        XRRayInteractor xrRayInteractor = rayInteractor as XRRayInteractor;
        if (xrController == null || xrRayInteractor == null) return;

        // 获取控制器输入
        bool selectPressed = false;
        bool menuPressed = false;
        bool triggerPressed = false;

        // 读取控制器按钮状态
        xrController.inputDevice.TryGetFeatureValue(UnityEngine.XR.CommonUsages.triggerButton, out triggerPressed);
        xrController.inputDevice.TryGetFeatureValue(UnityEngine.XR.CommonUsages.primaryButton, out selectPressed);
        xrController.inputDevice.TryGetFeatureValue(UnityEngine.XR.CommonUsages.menuButton, out menuPressed);

        // 处理选择输入（扳机键）
        if (triggerPressed && !isSelectPressed)
        {
            isSelectPressed = true;
            HandlePartSelection(xrRayInteractor);
        }
        else if (!triggerPressed)
        {
            isSelectPressed = false;
        }

        // 处理功能按钮
        if (selectPressed)
        {
            OnNextStepRequested?.Invoke();
            TriggerHapticFeedback(xrController);
        }

        if (menuPressed && !isMenuPressed)
        {
            isMenuPressed = true;
            HandleMenuInput();
        }
        else if (!menuPressed)
        {
            isMenuPressed = false;
        }
#endif
    }

    /// <summary>
    /// 处理零件选择
    /// </summary>
    private void HandlePartSelection(object rayInteractor)
    {
#if UNITY_XR_INTERACTION_TOOLKIT
        XRRayInteractor xrRayInteractor = rayInteractor as XRRayInteractor;
        if (xrRayInteractor != null && xrRayInteractor.TryGetCurrent3DRaycastHit(out RaycastHit hit))
        {
            // 检查是否在交互层级上
            if (((1 << hit.collider.gameObject.layer) & interactionLayerMask) == 0)
            {
                return;
            }

            // 查找AssemblyPart组件
            AssemblyPart part = hit.collider.GetComponent<AssemblyPart>();
            if (part == null)
            {
                part = hit.collider.GetComponentInParent<AssemblyPart>();
            }

            if (part != null)
            {
                SelectPart(part, xrRayInteractor);
            }
        }
#endif
    }

    /// <summary>
    /// 选择零件
    /// </summary>
    private void SelectPart(AssemblyPart part, object interactor)
    {
        currentSelectedPart = part;
#if UNITY_XR_INTERACTION_TOOLKIT
        activeInteractor = interactor as XRRayInteractor;
#endif

        Debug.Log($"[VRInputManager] 选择零件: {part.PartName}");

        // 触发选择事件
        OnPartSelected?.Invoke(part);

        // 触发触觉反馈
#if UNITY_XR_INTERACTION_TOOLKIT
        if (activeInteractor != null)
        {
            var controller = activeInteractor.GetComponent<XRController>();
            if (controller != null)
            {
                TriggerHapticFeedback(controller);
            }
        }
#endif
    }

    /// <summary>
    /// 处理菜单输入
    /// </summary>
    private void HandleMenuInput()
    {
        // 可以根据需要添加菜单功能
        Debug.Log("[VRInputManager] 菜单按钮按下");
        
        // 示例：重置功能
        OnResetRequested?.Invoke();
    }

    /// <summary>
    /// 触发触觉反馈
    /// </summary>
    private void TriggerHapticFeedback(object controller)
    {
#if UNITY_XR_INTERACTION_TOOLKIT
        XRController xrController = controller as XRController;
        if (!enableHapticFeedback || xrController == null) return;

        xrController.SendHapticImpulse(hapticIntensity, hapticDuration);
#endif
    }

    /// <summary>
    /// 获取当前选中的零件
    /// </summary>
    public AssemblyPart GetSelectedPart()
    {
        return currentSelectedPart;
    }

    /// <summary>
    /// 获取活动的射线交互器
    /// </summary>
    public object GetActiveInteractor()
    {
#if UNITY_XR_INTERACTION_TOOLKIT
        return activeInteractor;
#else
        return null;
#endif
    }

    /// <summary>
    /// 设置交互层级遮罩
    /// </summary>
    public void SetInteractionLayerMask(LayerMask mask)
    {
        interactionLayerMask = mask;
    }

    /// <summary>
    /// 启用/禁用触觉反馈
    /// </summary>
    public void SetHapticFeedback(bool enabled)
    {
        enableHapticFeedback = enabled;
    }

    /// <summary>
    /// 手动触发下一步
    /// </summary>
    public void TriggerNextStep()
    {
        OnNextStepRequested?.Invoke();
    }

    /// <summary>
    /// 手动触发重置
    /// </summary>
    public void TriggerReset()
    {
        OnResetRequested?.Invoke();
    }

    /// <summary>
    /// 手动触发重播
    /// </summary>
    public void TriggerReplay()
    {
        OnReplayRequested?.Invoke();
    }

    void OnDestroy()
    {
        // 清理事件
        OnPartSelected = null;
        OnNextStepRequested = null;
        OnResetRequested = null;
        OnReplayRequested = null;
    }
}
