using UnityEngine;
using System.Collections.Generic;
using System;

/// <summary>
/// 简化的外部数据接收器
/// 提供简单的方法来接收和处理外部装配数据
/// </summary>
public class SimpleExternalDataReceiver : MonoBehaviour
{
    [Header("组件引用")]
    [SerializeField] private Neo4jAssemblyController assemblyController;

    [Header("测试数据")]
    [SerializeField] private bool enableTestMode = true;
    [SerializeField] private TextAsset testDataFile;

    void Start()
    {
        // 自动查找装配控制器
        if (assemblyController == null)
        {
            assemblyController = FindObjectOfType<Neo4jAssemblyController>();
        }

        if (assemblyController == null)
        {
            Debug.LogError("[SimpleExternalDataReceiver] 未找到Neo4jAssemblyController组件！");
            enabled = false;
            return;
        }

        Debug.Log("[SimpleExternalDataReceiver] 外部数据接收器初始化完成");

        // 测试模式下加载测试数据
        if (enableTestMode && testDataFile != null)
        {
            LoadTestData();
        }
    }

    /// <summary>
    /// 接收外部装配数据的主要方法
    /// 外部系统可以调用此方法来发送装配数据
    /// </summary>
    /// <param name="partName">零件名称</param>
    /// <param name="jsonData">JSON格式的装配数据</param>
    public void ReceiveAssemblyData(string partName, string jsonData)
    {
        Debug.Log($"[SimpleExternalDataReceiver] 接收装配数据: {partName}");
        Debug.Log($"[SimpleExternalDataReceiver] 数据内容: {jsonData}");

        if (assemblyController != null)
        {
            // 直接调用装配控制器的方法
            assemblyController.ReceiveExternalAssemblyData(partName, jsonData);
        }
        else
        {
            Debug.LogError("[SimpleExternalDataReceiver] 装配控制器未找到！");
        }
    }

    /// <summary>
    /// 接收扁平图序列数据（新增方法）
    /// 专门处理任务1扁平图序列1.json这种格式的数据
    /// </summary>
    /// <param name="sequenceJsonData">扁平图序列JSON数据</param>
    public void ReceiveSequenceData(string sequenceJsonData)
    {
        Debug.Log($"[SimpleExternalDataReceiver] 接收扁平图序列数据");

        if (assemblyController == null)
        {
            Debug.LogError("[SimpleExternalDataReceiver] 装配控制器未找到！");
            return;
        }

        try
        {
            // 转换扁平图序列格式为系统内部格式
            string convertedData = ConvertSequenceToInternalFormat(sequenceJsonData);

            // 发送转换后的数据到装配控制器
            assemblyController.ReceiveExternalAssemblyData("SequenceAssembly", convertedData);

            Debug.Log("[SimpleExternalDataReceiver] 扁平图序列数据处理完成");
        }
        catch (System.Exception e)
        {
            Debug.LogError($"[SimpleExternalDataReceiver] 处理扁平图序列数据失败: {e.Message}");
        }
    }

    /// <summary>
    /// 接收简化格式的装配数据
    /// </summary>
    /// <param name="partName">零件名称</param>
    /// <param name="targetPart">目标零件</param>
    /// <param name="movingRefPoint">移动参考点</param>
    /// <param name="targetRefPoint">目标参考点</param>
    /// <param name="connectionType">连接类型</param>
    public void ReceiveSimpleAssemblyStep(string partName, string targetPart, 
        string movingRefPoint, string targetRefPoint, string connectionType = "DIRECT")
    {
        // 构建简单的JSON数据
        string jsonData = $@"{{
            ""steps"": [
                {{
                    ""movingPart"": ""{partName}"",
                    ""targetPart"": ""{targetPart}"",
                    ""movingRefPoint"": ""{movingRefPoint}"",
                    ""targetRefPoint"": ""{targetRefPoint}"",
                    ""connectionType"": ""{connectionType}"",
                    ""fastener"": {{
                        ""type"": ""NONE"",
                        ""screwType"": """",
                        ""nutType"": """"
                    }},
                    ""additionalMountPoints"": []
                }}
            ]
        }}";

        ReceiveAssemblyData(partName, jsonData);
    }

    /// <summary>
    /// 接收带螺丝螺母的装配数据
    /// </summary>
    /// <param name="partName">零件名称</param>
    /// <param name="targetPart">目标零件</param>
    /// <param name="movingRefPoint">移动参考点</param>
    /// <param name="targetRefPoint">目标参考点</param>
    /// <param name="screwType">螺丝类型</param>
    /// <param name="nutType">螺母类型</param>
    public void ReceiveAssemblyStepWithFastener(string partName, string targetPart,
        string movingRefPoint, string targetRefPoint, string screwType, string nutType = "nut")
    {
        string fastenerType = string.IsNullOrEmpty(nutType) ? "SCREW_ONLY" : "SCREW_NUT";

        string jsonData = $@"{{
            ""steps"": [
                {{
                    ""movingPart"": ""{partName}"",
                    ""targetPart"": ""{targetPart}"",
                    ""movingRefPoint"": ""{movingRefPoint}"",
                    ""targetRefPoint"": ""{targetRefPoint}"",
                    ""connectionType"": ""SCREW"",
                    ""fastener"": {{
                        ""type"": ""{fastenerType}"",
                        ""screwType"": ""{screwType}"",
                        ""nutType"": ""{nutType}""
                    }},
                    ""additionalMountPoints"": []
                }}
            ]
        }}";

        ReceiveAssemblyData(partName, jsonData);
    }

    /// <summary>
    /// 加载测试数据
    /// </summary>
    private void LoadTestData()
    {
        if (testDataFile == null)
        {
            Debug.LogWarning("[SimpleExternalDataReceiver] 测试数据文件未配置");
            return;
        }

        try
        {
            string testData = testDataFile.text;
            Debug.Log("[SimpleExternalDataReceiver] 加载测试数据");
            
            // 解析测试数据并发送
            ReceiveAssemblyData("TestPart", testData);
        }
        catch (System.Exception e)
        {
            Debug.LogError($"[SimpleExternalDataReceiver] 加载测试数据失败: {e.Message}");
        }
    }

    /// <summary>
    /// 转换扁平图序列格式为系统内部格式
    /// </summary>
    /// <param name="sequenceJsonData">扁平图序列JSON数据</param>
    /// <returns>转换后的内部格式JSON数据</returns>
    private string ConvertSequenceToInternalFormat(string sequenceJsonData)
    {
        var sequenceData = SimpleJSON.JSON.Parse(sequenceJsonData);
        var sequenceArray = sequenceData["sequence"];

        var stepsArray = new SimpleJSON.JSONArray();

        foreach (SimpleJSON.JSONNode sequenceStep in sequenceArray.AsArray)
        {
            var step = new SimpleJSON.JSONObject();

            // 基本信息
            step["movingPart"] = sequenceStep["movingPartName"];
            step["targetPart"] = sequenceStep["targetPartName"];
            step["movingRefPoint"] = sequenceStep["movingPartRefPoint"];
            step["targetRefPoint"] = sequenceStep["targetPartRefPoint"];
            step["connectionType"] = sequenceStep["connectionType"];

            // 紧固件信息
            var fastener = new SimpleJSON.JSONObject();
            fastener["type"] = sequenceStep["fastenerType"];
            fastener["screwType"] = sequenceStep["screwType"];
            fastener["nutType"] = sequenceStep["nutType"];
            step["fastener"] = fastener;

            // 额外装配点
            var additionalMountPoints = new SimpleJSON.JSONArray();
            if (sequenceStep["additionalMountPoints"] != null)
            {
                foreach (SimpleJSON.JSONNode mountPointStr in sequenceStep["additionalMountPoints"].AsArray)
                {
                    // 解析格式: "主机架反_P5,安装连接块反2_P0,SCREW,SCREW_NUT,M3X10-10,Nut"
                    string[] parts = mountPointStr.Value.Split(',');
                    if (parts.Length >= 6)
                    {
                        var mountPoint = new SimpleJSON.JSONObject();
                        mountPoint["partName"] = parts[0];
                        mountPoint["mountPoint"] = parts[1];
                        mountPoint["screwType"] = parts[4];
                        mountPoint["nutType"] = parts[5];
                        additionalMountPoints.Add(mountPoint);
                    }
                }
            }
            step["additionalMountPoints"] = additionalMountPoints;

            stepsArray.Add(step);
        }

        var result = new SimpleJSON.JSONObject();
        result["steps"] = stepsArray;

        return result.ToString();
    }

    /// <summary>
    /// 创建示例装配数据
    /// </summary>
    [ContextMenu("创建示例装配数据")]
    public void CreateSampleAssemblyData()
    {
        string sampleData = @"{
            ""steps"": [
                {
                    ""movingPart"": ""ShortUBracket"",
                    ""targetPart"": ""LongUBracket"",
                    ""movingRefPoint"": ""P1"",
                    ""targetRefPoint"": ""P1"",
                    ""connectionType"": ""SCREW"",
                    ""fastener"": {
                        ""type"": ""SCREW_NUT"",
                        ""screwType"": ""M2X6"",
                        ""nutType"": ""nut""
                    },
                    ""additionalMountPoints"": [
                        {
                            ""partName"": ""ShortUBracket"",
                            ""mountPoint"": ""P2"",
                            ""screwType"": ""M2X6"",
                            ""nutType"": ""nut""
                        }
                    ]
                }
            ]
        }";

        ReceiveAssemblyData("ShortUBracket", sampleData);
    }

    /// <summary>
    /// 测试扁平图序列数据（新增方法）
    /// </summary>
    [ContextMenu("测试扁平图序列数据")]
    public void TestSequenceData()
    {
        if (testDataFile != null)
        {
            ReceiveSequenceData(testDataFile.text);
        }
        else
        {
            Debug.LogWarning("[SimpleExternalDataReceiver] 测试数据文件未配置，请在Inspector中设置testDataFile");
        }
    }

    /// <summary>
    /// 测试简单装配步骤
    /// </summary>
    [ContextMenu("测试简单装配步骤")]
    public void TestSimpleAssemblyStep()
    {
        ReceiveSimpleAssemblyStep("ShortUBracket", "LongUBracket", "P1", "P1", "DIRECT");
    }

    /// <summary>
    /// 测试带螺丝的装配步骤
    /// </summary>
    [ContextMenu("测试螺丝装配步骤")]
    public void TestScrewAssemblyStep()
    {
        ReceiveAssemblyStepWithFastener("ShortUBracket", "LongUBracket", "P1", "P1", "M2X6", "nut");
    }

    /// <summary>
    /// 清除当前装配步骤
    /// </summary>
    [ContextMenu("清除装配步骤")]
    public void ClearAssemblySteps()
    {
        if (assemblyController != null)
        {
            assemblyController.ResetAssembly();
            Debug.Log("[SimpleExternalDataReceiver] 装配步骤已清除");
        }
    }

    /// <summary>
    /// 外部系统可以调用此方法来检查系统状态
    /// </summary>
    /// <returns>系统是否准备好接收数据</returns>
    public bool IsReadyToReceiveData()
    {
        return assemblyController != null && !assemblyController.HasRemainingSteps();
    }

    /// <summary>
    /// 获取当前装配状态
    /// </summary>
    /// <returns>装配状态信息</returns>
    public string GetAssemblyStatus()
    {
        if (assemblyController == null)
        {
            return "装配控制器未找到";
        }

        if (assemblyController.HasRemainingSteps())
        {
            return "装配进行中";
        }
        else
        {
            return "等待装配数据";
        }
    }

    void Update()
    {
        // 测试模式下的快捷键
        if (enableTestMode)
        {
            if (Input.GetKeyDown(KeyCode.T))
            {
                CreateSampleAssemblyData();
            }
            else if (Input.GetKeyDown(KeyCode.R))
            {
                ClearAssemblySteps();
            }
            else if (Input.GetKeyDown(KeyCode.Q))
            {
                TestSequenceData(); // 新增：Q键测试扁平图序列数据
            }
        }
    }
}
