using UnityEngine;

/// <summary>
/// 简单的装配数据接收器
/// 提供基本的数据接收功能，避免复杂的类型依赖
/// </summary>
public class SimpleAssemblyDataReceiver : MonoBehaviour
{
    [Header("组件引用")]
    [SerializeField] private Neo4jAssemblyController assemblyController;

    [Header("调试设置")]
    [SerializeField] private bool enableDebugMode = true;

    void Start()
    {
        // 自动查找装配控制器
        if (assemblyController == null)
        {
            assemblyController = FindObjectOfType<Neo4jAssemblyController>();
        }

        if (assemblyController == null)
        {
            Debug.LogError("[SimpleAssemblyDataReceiver] 未找到Neo4jAssemblyController组件！");
            enabled = false;
            return;
        }

        Debug.Log("[SimpleAssemblyDataReceiver] 简单装配数据接收器初始化完成");
    }

    /// <summary>
    /// 接收简单的装配步骤
    /// </summary>
    /// <param name="movingPart">移动零件名称</param>
    /// <param name="targetPart">目标零件名称</param>
    /// <param name="movingRefPoint">移动零件参考点</param>
    /// <param name="targetRefPoint">目标零件参考点</param>
    public void ReceiveSimpleAssemblyStep(string movingPart, string targetPart, 
        string movingRefPoint, string targetRefPoint)
    {
        if (enableDebugMode)
        {
            Debug.Log($"[SimpleAssemblyDataReceiver] 接收装配步骤: {movingPart}[{movingRefPoint}] -> {targetPart}[{targetRefPoint}]");
        }

        if (assemblyController != null)
        {
            // 创建简单的装配步骤
            var step = AssemblyStep.CreateDirect(movingPart, targetPart, movingRefPoint, targetRefPoint);
            
            // 使用反射或公共方法添加步骤
            TryAddStepToController(step);
        }
    }

    /// <summary>
    /// 接收带螺丝的装配步骤
    /// </summary>
    /// <param name="movingPart">移动零件名称</param>
    /// <param name="targetPart">目标零件名称</param>
    /// <param name="movingRefPoint">移动零件参考点</param>
    /// <param name="targetRefPoint">目标零件参考点</param>
    /// <param name="screwType">螺丝类型</param>
    /// <param name="nutType">螺母类型（可选）</param>
    public void ReceiveAssemblyStepWithScrew(string movingPart, string targetPart,
        string movingRefPoint, string targetRefPoint, string screwType, string nutType = "")
    {
        if (enableDebugMode)
        {
            Debug.Log($"[SimpleAssemblyDataReceiver] 接收螺丝装配步骤: {movingPart}[{movingRefPoint}] -> {targetPart}[{targetRefPoint}], 螺丝: {screwType}, 螺母: {nutType}");
        }

        if (assemblyController != null)
        {
            // 创建带螺丝的装配步骤
            var step = AssemblyStep.CreateWithScrew(movingPart, targetPart, movingRefPoint, targetRefPoint, screwType, nutType);
            
            // 使用反射或公共方法添加步骤
            TryAddStepToController(step);
        }
    }

    /// <summary>
    /// 尝试将步骤添加到控制器
    /// </summary>
    private void TryAddStepToController(AssemblyStep step)
    {
        try
        {
            // 由于AssemblyStep是Neo4jAssemblyController的私有类型，
            // 这里需要使用其他方法来传递数据
            
            if (enableDebugMode)
            {
                Debug.Log($"[SimpleAssemblyDataReceiver] 尝试添加装配步骤: {step}");
            }

            // 暂时使用日志输出，实际使用时需要根据具体实现调整
            Debug.LogWarning("[SimpleAssemblyDataReceiver] 需要实现具体的步骤添加逻辑");
            
        }
        catch (System.Exception e)
        {
            Debug.LogError($"[SimpleAssemblyDataReceiver] 添加装配步骤失败: {e.Message}");
        }
    }

    /// <summary>
    /// 清除所有装配步骤
    /// </summary>
    public void ClearAllSteps()
    {
        if (assemblyController != null)
        {
            assemblyController.ResetAssembly();
            Debug.Log("[SimpleAssemblyDataReceiver] 已清除所有装配步骤");
        }
    }

    /// <summary>
    /// 开始执行装配动画
    /// </summary>
    public void StartAssemblyAnimation()
    {
        if (assemblyController != null)
        {
            assemblyController.ExecuteNextStep();
            Debug.Log("[SimpleAssemblyDataReceiver] 开始执行装配动画");
        }
    }

    /// <summary>
    /// 检查系统状态
    /// </summary>
    /// <returns>系统状态信息</returns>
    public string GetSystemStatus()
    {
        if (assemblyController == null)
        {
            return "装配控制器未找到";
        }

        if (assemblyController.HasRemainingSteps())
        {
            return "装配进行中";
        }
        else
        {
            return "等待装配数据";
        }
    }

    /// <summary>
    /// 接收JSON格式的装配数据（简化版本）
    /// </summary>
    /// <param name="jsonData">JSON格式的装配数据</param>
    public void ReceiveAssemblyDataJSON(string jsonData)
    {
        if (enableDebugMode)
        {
            Debug.Log($"[SimpleAssemblyDataReceiver] 接收JSON装配数据: {jsonData}");
        }

        if (assemblyController != null)
        {
            // 调用控制器的公共方法
            assemblyController.ReceiveExternalAssemblyData("ExternalData", jsonData);
        }
    }

    /// <summary>
    /// 接收字符串数组格式的装配数据
    /// </summary>
    /// <param name="assemblySteps">装配步骤数组，格式：movingPart,targetPart,movingRefPoint,targetRefPoint[,connectionType[,screwType[,nutType]]]</param>
    public void ReceiveAssemblyDataArray(string[] assemblySteps)
    {
        if (enableDebugMode)
        {
            Debug.Log($"[SimpleAssemblyDataReceiver] 接收数组装配数据，步骤数: {assemblySteps.Length}");
        }

        // 清空现有步骤
        ClearAllSteps();

        // 逐个处理装配步骤
        foreach (string stepData in assemblySteps)
        {
            ParseAndReceiveStep(stepData);
        }
    }

    /// <summary>
    /// 解析并接收单个装配步骤
    /// </summary>
    private void ParseAndReceiveStep(string stepData)
    {
        try
        {
            string[] parts = stepData.Split(',');
            if (parts.Length >= 4)
            {
                string movingPart = parts[0].Trim();
                string targetPart = parts[1].Trim();
                string movingRefPoint = parts[2].Trim();
                string targetRefPoint = parts[3].Trim();
                string connectionType = parts.Length > 4 ? parts[4].Trim() : "DIRECT";
                string screwType = parts.Length > 5 ? parts[5].Trim() : "";
                string nutType = parts.Length > 6 ? parts[6].Trim() : "";

                if (connectionType == "SCREW" && !string.IsNullOrEmpty(screwType))
                {
                    ReceiveAssemblyStepWithScrew(movingPart, targetPart, movingRefPoint, targetRefPoint, screwType, nutType);
                }
                else
                {
                    ReceiveSimpleAssemblyStep(movingPart, targetPart, movingRefPoint, targetRefPoint);
                }
            }
            else
            {
                Debug.LogWarning($"[SimpleAssemblyDataReceiver] 装配步骤数据格式错误: {stepData}");
            }
        }
        catch (System.Exception e)
        {
            Debug.LogError($"[SimpleAssemblyDataReceiver] 解析装配步骤失败: {stepData}, 错误: {e.Message}");
        }
    }

    // 测试方法
    [ContextMenu("测试简单装配")]
    public void TestSimpleAssembly()
    {
        ReceiveSimpleAssemblyStep("ShortUBracket", "LongUBracket", "P1", "P1");
    }

    [ContextMenu("测试螺丝装配")]
    public void TestScrewAssembly()
    {
        ReceiveAssemblyStepWithScrew("ShortUBracket", "LongUBracket", "P1", "P1", "M2X6", "nut");
    }

    [ContextMenu("测试多步装配")]
    public void TestMultipleAssembly()
    {
        string[] steps = {
            "ShortUBracket,LongUBracket,P1,P1,SCREW,M2X6,nut",
            "BasePlate,LongUBracket,P1,P3,SCREW,BA_5"
        };
        ReceiveAssemblyDataArray(steps);
    }

    [ContextMenu("清除装配步骤")]
    public void TestClearSteps()
    {
        ClearAllSteps();
    }

    [ContextMenu("开始装配动画")]
    public void TestStartAnimation()
    {
        StartAssemblyAnimation();
    }
}
