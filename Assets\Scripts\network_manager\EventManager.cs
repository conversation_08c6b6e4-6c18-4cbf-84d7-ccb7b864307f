using System;
using UnityEngine;

/// <summary>
/// 一个轻量级的、全局的、基于C#委托的事件管理器。
/// 用于在不同系统模块之间进行解耦的通信。
///
/// 使用方法:
/// 1. 触发事件: EventManager.TriggerLoadScene("MySceneName");
/// 2. 监听事件: EventManager.OnLoadSceneRequested += MyFunction;
/// 3. 取消监听: EventManager.OnLoadSceneRequested -= MyFunction;
/// </summary>
public static class EventManager
{
    // --- 定义事件 ---

    // 当收到加载场景指令时触发
    // 参数: 场景名称 (string)
    public static event Action<string> OnLoadSceneRequested;

    // 当收到执行装配步骤指令时触发
    // 参数: 步骤数据 (SequenceStepData)
    public static event Action<SequenceStepData> OnAssemblyStepReceived;

    // 当收到透视指令时触发
    // 参数: 状态 "on" 或 "off" (string)
    public static event Action<string> OnSeeThroughToggled;

    // 当收到失败反馈指令时触发
    // 参数: 失败信息 (string)
    public static event Action<string> OnFailureFeedbackReceived;

    // 当收到序列完成指令时触发
    public static event Action OnSequenceCompleted;


    // --- 触发事件的公共方法 ---

    public static void TriggerLoadScene(string sceneId)
    {
        Debug.Log($"[EventManager] 触发事件: OnLoadSceneRequested, 场景ID: {sceneId}");
        OnLoadSceneRequested?.Invoke(sceneId);
    }

    public static void TriggerAssemblyStep(SequenceStepData stepData)
    {
        Debug.Log($"[EventManager] 触发事件: OnAssemblyStepReceived, 步骤: {stepData.stepIndex + 1}");
        OnAssemblyStepReceived?.Invoke(stepData);
    }

    public static void TriggerSeeThrough(string state)
    {
        Debug.Log($"[EventManager] 触发事件: OnSeeThroughToggled, 状态: {state}");
        OnSeeThroughToggled?.Invoke(state);
    }

    public static void TriggerFailureFeedback(string message)
    {
        Debug.Log($"[EventManager] 触发事件: OnFailureFeedbackReceived, 消息: {message}");
        OnFailureFeedbackReceived?.Invoke(message);
    }

    public static void TriggerSequenceCompleted()
    {
        Debug.Log($"[EventManager] 触发事件: OnSequenceCompleted");
        OnSequenceCompleted?.Invoke();
    }
} 