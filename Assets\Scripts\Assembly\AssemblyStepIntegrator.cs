using UnityEngine;
using System.Collections;

/// <summary>
/// 装配步骤集成器
/// 将AssemblyPartManager与现有的装配系统集成
/// </summary>
public class AssemblyStepIntegrator : MonoBehaviour
{
    [Header("组件引用")]
    [SerializeField] private AssemblyPartManager partManager;
    [SerializeField] private Neo4jAssemblyController assemblyController;
    [SerializeField] private HypergraphSequenceProcessor hypergraphProcessor;
    
    [Header("集成设置")]
    [SerializeField] private bool autoFindComponents = true;
    [SerializeField] private bool enableStepTracking = true;
    
    [Header("调试设置")]
    [SerializeField] private bool debugMode = true;
    
    // 当前步骤信息
    private int currentStepIndex = 0;
    private bool isProcessingStep = false;
    
    void Start()
    {
        InitializeIntegrator();
        SetupEventListeners();
    }
    
    /// <summary>
    /// 初始化集成器
    /// </summary>
    private void InitializeIntegrator()
    {
        if (autoFindComponents)
        {
            FindComponents();
        }
        
        ValidateComponents();
        
        if (debugMode)
        {
            Debug.Log("🔗 装配步骤集成器已初始化");
        }
    }
    
    /// <summary>
    /// 自动查找组件
    /// </summary>
    private void FindComponents()
    {
        if (partManager == null)
        {
            partManager = FindObjectOfType<AssemblyPartManager>();
        }
        
        if (assemblyController == null)
        {
            assemblyController = FindObjectOfType<Neo4jAssemblyController>();
        }
        
        if (hypergraphProcessor == null)
        {
            hypergraphProcessor = FindObjectOfType<HypergraphSequenceProcessor>();
        }
        
        if (debugMode)
        {
            Debug.Log($"🔍 组件查找结果:");
            Debug.Log($"   PartManager: {(partManager != null ? "✅" : "❌")}");
            Debug.Log($"   AssemblyController: {(assemblyController != null ? "✅" : "❌")}");
            Debug.Log($"   HypergraphProcessor: {(hypergraphProcessor != null ? "✅" : "❌")}");
        }
    }
    
    /// <summary>
    /// 验证组件
    /// </summary>
    private void ValidateComponents()
    {
        if (partManager == null)
        {
            Debug.LogError("❌ AssemblyPartManager 未找到！请确保场景中存在该组件。");
        }
        
        if (assemblyController == null && hypergraphProcessor == null)
        {
            Debug.LogWarning("⚠️ 未找到装配控制器！某些功能可能无法正常工作。");
        }
    }
    
    /// <summary>
    /// 设置事件监听器
    /// </summary>
    private void SetupEventListeners()
    {
        // 监听Neo4j装配控制器事件
        if (assemblyController != null)
        {
            // 这里需要根据Neo4jAssemblyController的实际事件来设置
            // 由于我们无法直接访问私有事件，我们将通过其他方式集成
            StartCoroutine(MonitorAssemblyController());
        }
        
        // 监听超图序列处理器事件
        if (hypergraphProcessor != null)
        {
            // 同样，这里需要根据实际的事件接口来设置
            StartCoroutine(MonitorHypergraphProcessor());
        }
        
        if (debugMode)
        {
            Debug.Log("📡 事件监听器已设置");
        }
    }
    
    /// <summary>
    /// 监控装配控制器状态
    /// </summary>
    private IEnumerator MonitorAssemblyController()
    {
        while (assemblyController != null)
        {
            // 这里可以监控装配控制器的状态变化
            // 由于无法直接访问私有字段，我们使用反射或其他方式
            yield return new WaitForSeconds(0.1f);
        }
    }
    
    /// <summary>
    /// 监控超图序列处理器状态
    /// </summary>
    private IEnumerator MonitorHypergraphProcessor()
    {
        while (hypergraphProcessor != null)
        {
            // 监控超图处理器的状态
            yield return new WaitForSeconds(0.1f);
        }
    }
    
    /// <summary>
    /// 处理装配步骤（公共接口，供其他脚本调用）
    /// </summary>
    /// <param name="movingPart">移动零件</param>
    /// <param name="targetPart">目标零件</param>
    /// <param name="fastenerType">紧固件类型</param>
    /// <param name="screwType">螺丝类型</param>
    /// <param name="nutType">螺母类型</param>
    public void ProcessAssemblyStep(string movingPart, string targetPart, 
        string fastenerType = "", string screwType = "", string nutType = "")
    {
        if (partManager == null)
        {
            Debug.LogError("❌ AssemblyPartManager 未设置！");
            return;
        }
        
        if (isProcessingStep)
        {
            Debug.LogWarning("⚠️ 正在处理其他步骤，请稍候...");
            return;
        }
        
        StartCoroutine(ProcessStepCoroutine(movingPart, targetPart, fastenerType, screwType, nutType));
    }
    
    /// <summary>
    /// 处理装配步骤的协程
    /// </summary>
    private IEnumerator ProcessStepCoroutine(string movingPart, string targetPart, 
        string fastenerType, string screwType, string nutType)
    {
        isProcessingStep = true;
        currentStepIndex++;
        
        if (debugMode)
        {
            Debug.Log($"🔧 开始处理装配步骤 {currentStepIndex}: {movingPart} -> {targetPart}");
        }
        
        // 使用零件管理器处理步骤
        partManager.ProcessAssemblyStep(movingPart, targetPart, fastenerType, screwType, nutType);
        
        // 等待一小段时间，确保UI更新
        yield return new WaitForSeconds(0.1f);
        
        if (debugMode)
        {
            Debug.Log($"✅ 装配步骤 {currentStepIndex} 处理完成");
        }
        
        isProcessingStep = false;
    }
    
    /// <summary>
    /// 从JSON数据处理装配步骤
    /// </summary>
    /// <param name="stepData">步骤数据（JSON格式或结构化数据）</param>
    public void ProcessAssemblyStepFromData(object stepData)
    {
        // 这里需要根据实际的数据格式来解析
        // 假设stepData是一个包含装配信息的对象
        
        if (stepData == null)
        {
            Debug.LogError("❌ 步骤数据为空！");
            return;
        }
        
        // 尝试解析数据（这里需要根据实际数据结构调整）
        try
        {
            // 示例解析逻辑，需要根据实际数据格式调整
            string movingPart = "";
            string targetPart = "";
            string fastenerType = "";
            string screwType = "";
            string nutType = "";
            
            // 如果是Neo4jAssemblyController.AssemblyStep类型
            if (stepData is Neo4jAssemblyController.AssemblyStep)
            {
                var step = (Neo4jAssemblyController.AssemblyStep)stepData;
                movingPart = step.movingPartName;
                targetPart = step.targetPartName;
                fastenerType = step.connectionType;
                screwType = step.fastener.screwType;
                nutType = step.fastener.nutType;
            }
            
            ProcessAssemblyStep(movingPart, targetPart, fastenerType, screwType, nutType);
        }
        catch (System.Exception e)
        {
            Debug.LogError($"❌ 解析装配步骤数据时出错: {e.Message}");
        }
    }
    
    /// <summary>
    /// 重置装配进度
    /// </summary>
    public void ResetAssemblyProgress()
    {
        if (partManager != null)
        {
            partManager.ResetAllParts();
        }
        
        currentStepIndex = 0;
        isProcessingStep = false;
        
        if (debugMode)
        {
            Debug.Log("🔄 装配进度已重置");
        }
    }
    
    /// <summary>
    /// 获取当前装配状态
    /// </summary>
    /// <returns>状态信息</returns>
    public string GetAssemblyStatus()
    {
        string status = $"当前步骤: {currentStepIndex}\n";
        status += $"正在处理: {(isProcessingStep ? "是" : "否")}\n";
        
        if (partManager != null)
        {
            status += partManager.GetStatusInfo();
        }
        
        return status;
    }
    
    /// <summary>
    /// 手动触发装配步骤（用于测试）
    /// </summary>
    [ContextMenu("测试装配步骤")]
    public void TestAssemblyStep()
    {
        ProcessAssemblyStep("测试零件A", "测试零件B", "SCREW", "M6螺丝", "M6螺母");
    }
    
    void OnDestroy()
    {
        // 清理事件监听器
        StopAllCoroutines();
    }
}
