using UnityEngine;

/// <summary>
/// 序列测试设置工具
/// 帮助快速设置序列执行测试环境
/// </summary>
public class SequenceTestSetup : MonoBehaviour
{
    [Header("自动设置")]
    [SerializeField] private bool autoSetupOnStart = true;
    [SerializeField] private TextAsset sequenceDataFile; // 任务1扁平图序列1.json
    
    [Header("组件预制体")]
    [SerializeField] private GameObject sequenceExecutorPrefab;
    
    void Start()
    {
        if (autoSetupOnStart)
        {
            SetupSequenceTest();
        }
    }
    
    /// <summary>
    /// 设置序列测试环境
    /// </summary>
    [ContextMenu("设置序列测试环境")]
    public void SetupSequenceTest()
    {
        Debug.Log("[SequenceTestSetup] 开始设置序列测试环境...");
        
        // 1. 查找或创建必要的组件
        SetupAssemblyController();
        SetupAnimationManager();
        SetupSequenceExecutor();
        
        Debug.Log("[SequenceTestSetup] 序列测试环境设置完成！");
        Debug.Log("使用说明：");
        Debug.Log("- 按 空格键 执行下一个装配步骤");
        Debug.Log("- 按 R键 重置装配序列");
    }
    
    /// <summary>
    /// 设置装配控制器
    /// </summary>
    private void SetupAssemblyController()
    {
        var controller = FindObjectOfType<Neo4jAssemblyController>();
        if (controller == null)
        {
            Debug.LogWarning("[SequenceTestSetup] 未找到Neo4jAssemblyController，请手动添加");
        }
        else
        {
            Debug.Log("[SequenceTestSetup] ✓ 找到Neo4jAssemblyController");
            
            // 设置为使用外部数据源
            var controllerType = controller.GetType();
            var useExternalField = controllerType.GetField("useExternalDataSource", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            if (useExternalField != null)
            {
                useExternalField.SetValue(controller, true);
                Debug.Log("[SequenceTestSetup] ✓ 已设置为使用外部数据源");
            }
        }
    }
    
    /// <summary>
    /// 设置动画管理器
    /// </summary>
    private void SetupAnimationManager()
    {
        var animationManager = FindObjectOfType<AssemblyAnimationManager>();
        if (animationManager == null)
        {
            Debug.LogWarning("[SequenceTestSetup] 未找到AssemblyAnimationManager，请手动添加");
        }
        else
        {
            Debug.Log("[SequenceTestSetup] ✓ 找到AssemblyAnimationManager");
        }
    }
    
    /// <summary>
    /// 设置序列执行器
    /// </summary>
    private void SetupSequenceExecutor()
    {
        var executor = FindObjectOfType<SimpleSequenceExecutor>();
        if (executor == null)
        {
            // 创建新的序列执行器
            GameObject executorObj = new GameObject("SequenceExecutor");
            executor = executorObj.AddComponent<SimpleSequenceExecutor>();
            Debug.Log("[SequenceTestSetup] ✓ 创建了SimpleSequenceExecutor");
        }
        else
        {
            Debug.Log("[SequenceTestSetup] ✓ 找到现有的SimpleSequenceExecutor");
        }
        
        // 设置序列数据文件
        if (sequenceDataFile != null)
        {
            var executorType = executor.GetType();
            var dataFileField = executorType.GetField("sequenceDataFile", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            if (dataFileField != null)
            {
                dataFileField.SetValue(executor, sequenceDataFile);
                Debug.Log($"[SequenceTestSetup] ✓ 设置序列数据文件: {sequenceDataFile.name}");
            }
        }
        else
        {
            Debug.LogWarning("[SequenceTestSetup] 序列数据文件未配置，请在Inspector中设置");
        }
    }
    
    /// <summary>
    /// 验证场景中的零件设置
    /// </summary>
    [ContextMenu("验证零件设置")]
    public void ValidatePartsSetup()
    {
        Debug.Log("[SequenceTestSetup] 开始验证零件设置...");
        
        var assemblyParts = FindObjectsOfType<AssemblyPart>();
        Debug.Log($"[SequenceTestSetup] 找到 {assemblyParts.Length} 个装配零件");
        
        int validParts = 0;
        int invalidParts = 0;
        
        foreach (var part in assemblyParts)
        {
            bool isValid = true;
            string issues = "";
            
            // 检查参考点
            if (!part.HasReferencePoints)
            {
                isValid = false;
                issues += "缺少参考点; ";
            }
            
            // 检查零件名称
            if (string.IsNullOrEmpty(part.PartName))
            {
                isValid = false;
                issues += "零件名称为空; ";
            }
            
            if (isValid)
            {
                validParts++;
                Debug.Log($"[SequenceTestSetup] ✓ {part.PartName} - 配置正确");
            }
            else
            {
                invalidParts++;
                Debug.LogWarning($"[SequenceTestSetup] ✗ {part.PartName} - 问题: {issues}");
            }
        }
        
        Debug.Log($"[SequenceTestSetup] 验证完成: {validParts} 个有效, {invalidParts} 个有问题");
        
        if (invalidParts > 0)
        {
            Debug.LogWarning("[SequenceTestSetup] 请修复有问题的零件配置后再进行测试");
        }
        else
        {
            Debug.Log("[SequenceTestSetup] ✓ 所有零件配置正确，可以开始测试");
        }
    }
    
    /// <summary>
    /// 检查序列数据中的零件是否都存在于场景中
    /// </summary>
    [ContextMenu("检查序列数据匹配")]
    public void CheckSequenceDataMatch()
    {
        if (sequenceDataFile == null)
        {
            Debug.LogWarning("[SequenceTestSetup] 序列数据文件未配置");
            return;
        }
        
        Debug.Log("[SequenceTestSetup] 开始检查序列数据匹配...");
        
        try
        {
            var jsonObject = SimpleJSON.JSON.Parse(sequenceDataFile.text);
            var sequenceArray = jsonObject["sequence"];
            
            var scenePartNames = new System.Collections.Generic.HashSet<string>();
            var assemblyParts = FindObjectsOfType<AssemblyPart>();
            foreach (var part in assemblyParts)
            {
                scenePartNames.Add(part.PartName);
            }
            
            var missingParts = new System.Collections.Generic.HashSet<string>();
            int totalSteps = 0;
            
            foreach (SimpleJSON.JSONNode stepNode in sequenceArray.AsArray)
            {
                totalSteps++;
                string movingPart = stepNode["movingPartName"];
                string targetPart = stepNode["targetPartName"];
                
                if (!scenePartNames.Contains(movingPart))
                {
                    missingParts.Add(movingPart);
                }
                
                if (!scenePartNames.Contains(targetPart))
                {
                    missingParts.Add(targetPart);
                }
            }
            
            Debug.Log($"[SequenceTestSetup] 序列包含 {totalSteps} 个步骤");
            Debug.Log($"[SequenceTestSetup] 场景中有 {scenePartNames.Count} 个零件");
            
            if (missingParts.Count == 0)
            {
                Debug.Log("[SequenceTestSetup] ✓ 所有序列中的零件都在场景中找到了");
            }
            else
            {
                Debug.LogWarning($"[SequenceTestSetup] ✗ 缺少 {missingParts.Count} 个零件:");
                foreach (string missingPart in missingParts)
                {
                    Debug.LogWarning($"  - {missingPart}");
                }
            }
        }
        catch (System.Exception e)
        {
            Debug.LogError($"[SequenceTestSetup] 检查序列数据时出错: {e.Message}");
        }
    }
    
    /// <summary>
    /// 创建测试用的简单零件
    /// </summary>
    [ContextMenu("创建测试零件")]
    public void CreateTestParts()
    {
        Debug.Log("[SequenceTestSetup] 创建测试零件...");
        
        // 创建一个简单的测试零件
        GameObject testPart = GameObject.CreatePrimitive(PrimitiveType.Cube);
        testPart.name = "TestPart";
        testPart.transform.position = Vector3.zero;
        
        // 添加AssemblyPart组件
        var assemblyPart = testPart.AddComponent<AssemblyPart>();
        
        // 创建参考点
        GameObject refPoint = new GameObject("P1");
        refPoint.transform.SetParent(testPart.transform);
        refPoint.transform.localPosition = Vector3.zero;
        
        // 设置参考点数组
        var refPointsField = assemblyPart.GetType().GetField("referencePoints", 
            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        if (refPointsField != null)
        {
            refPointsField.SetValue(assemblyPart, new Transform[] { refPoint.transform });
        }
        
        Debug.Log("[SequenceTestSetup] ✓ 创建了测试零件: TestPart");
    }
}
