using UnityEngine;

namespace Woz.Logging
{
    /// <summary>
    /// 将日志输出到Unity控制台
    /// </summary>
    public class ConsoleAppender : ILogAppender
    {
        private readonly ILogFormatter _formatter;

        public ConsoleAppender(ILogFormatter formatter)
        {
            _formatter = formatter;
        }

        public void Log(LogEntry entry)
        {
            string message = _formatter.Format(entry);
            switch (entry.Level)
            {
                case LogLevel.DEBUG:
                case LogLevel.INFO:
                    Debug.Log(message);
                    break;
                case LogLevel.WARNING:
                    Debug.LogWarning(message);
                    break;
                case LogLevel.ERROR:
                case LogLevel.FATAL:
                    Debug.LogError(message);
                    break;
            }
        }

        public void Dispose()
        {
            // 控制台输出器无需特殊清理
        }
    }

    /// <summary>
    /// 日志格式化器接口
    /// </summary>
    public interface ILogFormatter
    {
        string Format(LogEntry entry);
    }

    /// <summary>
    /// 默认的日志格式化器
    /// 格式: [YYYY-MM-DD HH:mm:ss.fff] [LEVEL] [Source] - Message
    /// </summary>
    public class DefaultLogFormatter : ILogFormatter
    {
        public string Format(LogEntry entry)
        {
            return $"[{entry.Timestamp:yyyy-MM-dd HH:mm:ss.fff}] [{entry.Level}] [{entry.Source}] - {entry.Message}";
        }
    }
} 