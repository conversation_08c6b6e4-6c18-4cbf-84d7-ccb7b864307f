using UnityEngine;
using System.Collections.Generic;
using System;

// --- 实验数据结构 ---
[System.Serializable]
public class GazeData
{
    public string target_area; // "assembly_hint", "text_hint", "other"
    public float duration; // 注视持续时间（秒）
    public DateTime timestamp;
    public Vector3 gaze_direction;
    public Vector3 head_position;
}

[System.Serializable]
public class StepErrorData
{
    public int step_index;
    public DateTime error_timestamp;
    public string error_type; // "assembly_failure", "part_selection_error", etc.
}

[System.Serializable]
public class ExperimentSessionData
{
    public string session_id;
    public string sequence_id;
    public DateTime session_start_time;
    public DateTime session_end_time;
    public List<GazeData> gaze_events = new List<GazeData>();
    public List<StepErrorData> error_events = new List<StepErrorData>();
    public Dictionary<int, float> step_completion_times = new Dictionary<int, float>();
}

public class ExperimentManager : MonoBehaviour
{
    [Header("实验配置")]
    public LayerMask assemblyHintLayer;
    public LayerMask textHintLayer;
    
    private ExperimentSessionData currentSession;
    private WebSocketManager wsManager;
    private Camera vrCamera;
    private string currentGazeTarget = "other";
    private float gazeStartTime;
    private int currentStepIndex = -1;
    private float stepStartTime;
    
    void Start()
    {
        wsManager = FindObjectOfType<WebSocketManager>();
        vrCamera = Camera.main; // 或者您的VR摄像机
        
        if (wsManager == null)
        {
            Debug.LogError("【实验】未找到WebSocketManager！实验管理器需要WebSocketManager组件。");
        }
    }
    
    void Update()
    {
        if (currentSession != null)
        {
            TrackGaze();
        }
    }
    
    // --- 公共接口 ---
    public void StartExperimentSession(string sessionId, string sequenceId)
    {
        currentSession = new ExperimentSessionData
        {
            session_id = sessionId,
            sequence_id = sequenceId,
            session_start_time = DateTime.Now
        };
        
        Debug.Log($"【实验】开始实验会话：{sessionId}，序列ID：{sequenceId}");
        SendExperimentEvent("session_started", currentSession);
    }
    
    public void OnStepStarted(int stepIndex)
    {
        currentStepIndex = stepIndex;
        stepStartTime = Time.time;
        Debug.Log($"【实验】步骤 {stepIndex} 开始，时间：{stepStartTime}");
    }
    
    public void OnStepCompleted(int stepIndex, bool success)
    {
        if (currentStepIndex == stepIndex)
        {
            float completionTime = Time.time - stepStartTime;
            currentSession.step_completion_times[stepIndex] = completionTime;
            
            var stepData = new {
                step_index = stepIndex,
                completion_time = completionTime,
                success = success,
                timestamp = DateTime.Now
            };
            
            SendExperimentEvent("step_completed", stepData);
            Debug.Log($"【实验】步骤 {stepIndex} 完成，耗时：{completionTime:F2}秒，成功：{success}");
        }
    }
    
    public void OnStepError(int stepIndex, string errorType)
    {
        var errorData = new StepErrorData
        {
            step_index = stepIndex,
            error_timestamp = DateTime.Now,
            error_type = errorType
        };
        
        currentSession.error_events.Add(errorData);
        SendExperimentEvent("step_error", errorData);
        Debug.Log($"【实验】步骤 {stepIndex} 发生错误：{errorType}");
    }
    
    public void EndExperimentSession()
    {
        if (currentSession != null)
        {
            currentSession.session_end_time = DateTime.Now;
            SendExperimentEvent("session_ended", currentSession);
            Debug.Log($"【实验】结束实验会话：{currentSession.session_id}");
            currentSession = null;
        }
    }
    
    // --- 私有方法 ---
    private void TrackGaze()
    {
        if (vrCamera == null) return;
        
        Ray gazeRay = new Ray(vrCamera.transform.position, vrCamera.transform.forward);
        RaycastHit hit;
        
        string newTarget = "other";
        
        if (Physics.Raycast(gazeRay, out hit))
        {
            if (((1 << hit.collider.gameObject.layer) & assemblyHintLayer) != 0)
            {
                newTarget = "assembly_hint";
            }
            else if (((1 << hit.collider.gameObject.layer) & textHintLayer) != 0)
            {
                newTarget = "text_hint";
            }
        }
        
        // 检测注视目标变化
        if (newTarget != currentGazeTarget)
        {
            // 结束前一个注视事件
            if (currentGazeTarget != "other")
            {
                float duration = Time.time - gazeStartTime;
                RecordGazeEvent(currentGazeTarget, duration);
            }
            
            // 开始新的注视事件
            currentGazeTarget = newTarget;
            gazeStartTime = Time.time;
        }
    }
    
    private void RecordGazeEvent(string targetArea, float duration)
    {
        var gazeData = new GazeData
        {
            target_area = targetArea,
            duration = duration,
            timestamp = DateTime.Now,
            gaze_direction = vrCamera.transform.forward,
            head_position = vrCamera.transform.position
        };
        
        currentSession.gaze_events.Add(gazeData);
        
        // 只有持续时间超过阈值才发送到服务器
        if (duration > 0.5f) // 500ms阈值
        {
            SendExperimentEvent("gaze_event", gazeData);
        }
    }
    
    private void SendExperimentEvent(string eventType, object eventData)
    {
        if (wsManager != null)
        {
            var message = new {
                message_type = "experiment_data",
                event_type = eventType,
                session_id = currentSession?.session_id,
                timestamp = DateTime.Now,
                data = eventData
            };
            
            string jsonMessage = JsonUtility.ToJson(message);
            // 现在实际发送实验数据到服务器
            wsManager.SendExperimentData(jsonMessage);
            Debug.Log($"【实验】实验事件已发送：{eventType} - {jsonMessage}");
        }
        else
        {
            Debug.LogWarning($"【实验】无法发送实验事件 '{eventType}'：WebSocketManager不可用");
        }
    }
} 