using UnityEngine;

/// <summary>
/// VR模拟器 - 在Unity编辑器中模拟PICO VR体验
/// 允许开发者在编辑器中测试VR装配系统的视角和交互
/// </summary>
public class VRSimulator : MonoBehaviour
{
    [Header("VR模拟设置")]
    [SerializeField] private bool enableVRSimulation = true;
    [SerializeField] private bool simulateHeadTracking = true;
    [SerializeField] private bool simulateControllers = true;

    [Header("头部跟踪模拟")]
    [SerializeField] private float headMovementSpeed = 5f; // 增加移动速度
    [SerializeField] private float headRotationSpeed = 60f;
    [SerializeField] private Vector3 headPositionRange = new Vector3(0.5f, 0.3f, 0.5f);
    [SerializeField] private Vector3 headRotationRange = new Vector3(45f, 90f, 30f);
    [SerializeField] private bool enableMovementLimits = false; // 默认禁用移动限制

    [Header("控制器模拟")]
    [SerializeField] private Transform leftControllerSimulator;
    [SerializeField] private Transform rightControllerSimulator;
    [SerializeField] private float controllerMovementSpeed = 3f;

    [Header("VR摄像机设置")]
    [SerializeField] private Camera vrCamera;
    [SerializeField] private float vrCameraHeight = 1.7f; // 模拟用户身高
    [SerializeField] private Vector3 vrPlayArea = new Vector3(10f, 5f, 10f); // 扩大VR活动区域

    [Header("输入映射")]
    [SerializeField] private KeyCode moveForwardKey = KeyCode.W;
    [SerializeField] private KeyCode moveBackKey = KeyCode.S;
    [SerializeField] private KeyCode moveLeftKey = KeyCode.A;
    [SerializeField] private KeyCode moveRightKey = KeyCode.D;
    [SerializeField] private KeyCode moveUpKey = KeyCode.Q;
    [SerializeField] private KeyCode moveDownKey = KeyCode.E;

    [Header("调试显示")]
    [SerializeField] private bool showVRBounds = true;
    [SerializeField] private bool showControllerRays = true;
    [SerializeField] private Color vrBoundsColor = Color.cyan;

    // 内部状态
    private Vector3 originalCameraPosition;
    private Quaternion originalCameraRotation;
    private Vector3 headOffset = Vector3.zero;
    private bool isSimulating = false;

    void Start()
    {
        InitializeVRSimulator();
    }

    void Update()
    {
        if (enableVRSimulation && Application.isEditor)
        {
            HandleVRSimulation();
        }
    }

    /// <summary>
    /// 初始化VR模拟器
    /// </summary>
    private void InitializeVRSimulator()
    {
        // 查找VR摄像机
        if (vrCamera == null)
        {
            vrCamera = Camera.main;
        }

        if (vrCamera != null)
        {
            originalCameraPosition = vrCamera.transform.position;
            originalCameraRotation = vrCamera.transform.rotation;

            // 设置VR摄像机高度
            Vector3 pos = vrCamera.transform.position;
            pos.y = vrCameraHeight;
            vrCamera.transform.position = pos;

            Debug.Log($"[VRSimulator] VR模拟器初始化完成，摄像机高度: {vrCameraHeight}m");
        }

        // 创建控制器模拟器
        CreateControllerSimulators();

        isSimulating = true;
    }

    /// <summary>
    /// 创建控制器模拟器
    /// </summary>
    private void CreateControllerSimulators()
    {
        if (simulateControllers && vrCamera != null)
        {
            // 左控制器
            if (leftControllerSimulator == null)
            {
                GameObject leftController = new GameObject("LeftController_Simulator");
                leftControllerSimulator = leftController.transform;
                leftControllerSimulator.SetParent(vrCamera.transform);
                leftControllerSimulator.localPosition = new Vector3(-0.3f, -0.3f, 0.3f);
                leftControllerSimulator.localRotation = Quaternion.Euler(-30f, -15f, 0f);

                // 添加可视化
                CreateControllerVisual(leftControllerSimulator, Color.blue);
            }

            // 右控制器
            if (rightControllerSimulator == null)
            {
                GameObject rightController = new GameObject("RightController_Simulator");
                rightControllerSimulator = rightController.transform;
                rightControllerSimulator.SetParent(vrCamera.transform);
                rightControllerSimulator.localPosition = new Vector3(0.3f, -0.3f, 0.3f);
                rightControllerSimulator.localRotation = Quaternion.Euler(-30f, 15f, 0f);

                // 添加可视化
                CreateControllerVisual(rightControllerSimulator, Color.red);
            }
        }
    }

    /// <summary>
    /// 创建控制器可视化
    /// </summary>
    private void CreateControllerVisual(Transform controller, Color color)
    {
        // 创建简单的控制器模型
        GameObject visual = GameObject.CreatePrimitive(PrimitiveType.Capsule);
        visual.transform.SetParent(controller);
        visual.transform.localPosition = Vector3.zero;
        visual.transform.localRotation = Quaternion.identity;
        visual.transform.localScale = new Vector3(0.05f, 0.1f, 0.05f);

        // 设置材质
        Renderer renderer = visual.GetComponent<Renderer>();
        Material mat = new Material(Shader.Find("Standard"));
        mat.color = color;
        renderer.material = mat;

        // 移除碰撞器
        Collider collider = visual.GetComponent<Collider>();
        if (collider != null) DestroyImmediate(collider);
    }

    /// <summary>
    /// 处理VR模拟
    /// </summary>
    private void HandleVRSimulation()
    {
        if (vrCamera == null) return;

        // 模拟头部移动
        if (simulateHeadTracking)
        {
            HandleHeadMovement();
            HandleHeadRotation();
        }

        // 模拟控制器
        if (simulateControllers)
        {
            HandleControllerSimulation();
        }
    }

    /// <summary>
    /// 处理头部移动模拟
    /// </summary>
    private void HandleHeadMovement()
    {
        Vector3 movement = Vector3.zero;

        // WASD移动
        if (Input.GetKey(moveForwardKey)) movement += vrCamera.transform.forward;
        if (Input.GetKey(moveBackKey)) movement -= vrCamera.transform.forward;
        if (Input.GetKey(moveLeftKey)) movement -= vrCamera.transform.right;
        if (Input.GetKey(moveRightKey)) movement += vrCamera.transform.right;
        if (Input.GetKey(moveUpKey)) movement += Vector3.up;
        if (Input.GetKey(moveDownKey)) movement -= Vector3.up;

        // 应用移动
        if (movement != Vector3.zero)
        {
            movement = movement.normalized * headMovementSpeed * Time.deltaTime;
            vrCamera.transform.position += movement;

            // 可选的限制在VR活动区域内
            if (enableMovementLimits)
            {
                ClampToVRBounds();
            }
        }
    }

    /// <summary>
    /// 处理头部旋转模拟
    /// </summary>
    private void HandleHeadRotation()
    {
        // 鼠标右键拖拽旋转视角
        if (Input.GetMouseButton(1))
        {
            float mouseX = Input.GetAxis("Mouse X") * headRotationSpeed * Time.deltaTime;
            float mouseY = Input.GetAxis("Mouse Y") * headRotationSpeed * Time.deltaTime;

            // 水平旋转
            vrCamera.transform.Rotate(Vector3.up, mouseX, Space.World);

            // 垂直旋转（限制范围）
            Vector3 currentRotation = vrCamera.transform.eulerAngles;
            float newXRotation = currentRotation.x - mouseY;

            // 处理角度范围
            if (newXRotation > 180f) newXRotation -= 360f;
            newXRotation = Mathf.Clamp(newXRotation, -headRotationRange.x, headRotationRange.x);

            vrCamera.transform.eulerAngles = new Vector3(newXRotation, currentRotation.y, currentRotation.z);
        }
    }

    /// <summary>
    /// 处理控制器模拟
    /// </summary>
    private void HandleControllerSimulation()
    {
        // 这里可以添加控制器的具体模拟逻辑
        // 比如模拟控制器的移动、旋转等
    }

    /// <summary>
    /// 限制摄像机在VR边界内
    /// </summary>
    private void ClampToVRBounds()
    {
        Vector3 pos = vrCamera.transform.position;
        Vector3 center = originalCameraPosition;

        pos.x = Mathf.Clamp(pos.x, center.x - vrPlayArea.x/2, center.x + vrPlayArea.x/2);
        pos.y = Mathf.Clamp(pos.y, center.y, center.y + vrPlayArea.y);
        pos.z = Mathf.Clamp(pos.z, center.z - vrPlayArea.z/2, center.z + vrPlayArea.z/2);

        vrCamera.transform.position = pos;
    }

    /// <summary>
    /// 获取模拟的VR摄像机
    /// </summary>
    public Camera GetVRCamera()
    {
        return vrCamera;
    }

    /// <summary>
    /// 获取模拟的控制器位置
    /// </summary>
    public Transform GetControllerTransform(bool isRightHand)
    {
        return isRightHand ? rightControllerSimulator : leftControllerSimulator;
    }

    /// <summary>
    /// 重置VR位置
    /// </summary>
    [ContextMenu("重置VR位置")]
    public void ResetVRPosition()
    {
        if (vrCamera != null)
        {
            vrCamera.transform.position = originalCameraPosition;
            vrCamera.transform.rotation = originalCameraRotation;
            
            Vector3 pos = vrCamera.transform.position;
            pos.y = vrCameraHeight;
            vrCamera.transform.position = pos;

            Debug.Log("[VRSimulator] VR位置已重置");
        }
    }

    /// <summary>
    /// 切换VR模拟
    /// </summary>
    [ContextMenu("切换VR模拟")]
    public void ToggleVRSimulation()
    {
        enableVRSimulation = !enableVRSimulation;
        Debug.Log($"[VRSimulator] VR模拟: {(enableVRSimulation ? "启用" : "禁用")}");
    }

    /// <summary>
    /// 模拟用户身高调整
    /// </summary>
    public void SetUserHeight(float height)
    {
        vrCameraHeight = height;
        if (vrCamera != null)
        {
            Vector3 pos = vrCamera.transform.position;
            pos.y = vrCameraHeight;
            vrCamera.transform.position = pos;
        }
        Debug.Log($"[VRSimulator] 用户身高设置为: {height}m");
    }

    // 绘制VR边界和调试信息
    void OnDrawGizmos()
    {
        if (!showVRBounds) return;

        // 绘制VR活动区域
        Gizmos.color = vrBoundsColor;
        Vector3 center = originalCameraPosition;
        Gizmos.DrawWireCube(center + Vector3.up * vrPlayArea.y/2, vrPlayArea);

        // 绘制用户高度线
        Gizmos.color = Color.green;
        Gizmos.DrawLine(center, center + Vector3.up * vrCameraHeight);

        // 绘制控制器射线
        if (showControllerRays && simulateControllers)
        {
            if (leftControllerSimulator != null)
            {
                Gizmos.color = Color.blue;
                Gizmos.DrawRay(leftControllerSimulator.position, leftControllerSimulator.forward * 2f);
            }

            if (rightControllerSimulator != null)
            {
                Gizmos.color = Color.red;
                Gizmos.DrawRay(rightControllerSimulator.position, rightControllerSimulator.forward * 2f);
            }
        }
    }

    void OnGUI()
    {
        if (!enableVRSimulation || !Application.isEditor) return;

        // 显示VR模拟器控制说明
        GUILayout.BeginArea(new Rect(10, 10, 300, 200));
        GUILayout.Label("VR模拟器控制:", EditorGUIStyle());
        GUILayout.Label("WASD - 移动");
        GUILayout.Label("QE - 上下移动");
        GUILayout.Label("右键拖拽 - 旋转视角");
        GUILayout.Label($"当前高度: {vrCamera?.transform.position.y:F2}m");
        
        if (GUILayout.Button("重置位置"))
        {
            ResetVRPosition();
        }
        
        GUILayout.EndArea();
    }

    private GUIStyle EditorGUIStyle()
    {
        GUIStyle style = new GUIStyle(GUI.skin.label);
        style.normal.textColor = Color.white;
        return style;
    }
}
