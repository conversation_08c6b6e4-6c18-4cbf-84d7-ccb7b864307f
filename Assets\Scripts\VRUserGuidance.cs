using UnityEngine;
using System.Collections;
using UnityEngine.UI;

/// <summary>
/// VR用户引导系统
/// 
/// 通过视觉提示、语音指导等方式引导用户移动到最佳观看位置，
/// 确保用户能清楚看到装配过程的每个细节
/// </summary>
public class VRUserGuidance : MonoBehaviour
{
    [Header("引导元素")]
    [SerializeField] private GameObject arrowPrefab;
    [SerializeField] private GameObject floorIndicatorPrefab;
    [SerializeField] private Material glowMaterial;
    [SerializeField] private Material highlightMaterial;
    
    [Header("引导设置")]
    [SerializeField] private float guidanceDistance = 0.5f;
    [SerializeField] private float guidanceHeight = 0.1f;
    [SerializeField] private float pulseSpeed = 2f;
    [SerializeField] private Color guidanceColor = Color.green;
    
    [Header("语音引导")]
    [SerializeField] private AudioSource audioSource;
    [SerializeField] private bool enableVoiceGuidance = true;
    [SerializeField] private float voiceVolume = 0.8f;
    
    [Header("UI提示")]
    [SerializeField] private Canvas guidanceCanvas;
    [SerializeField] private Text guidanceText;
    [SerializeField] private Image guidanceArrow;
    
    // 内部状态
    private GameObject currentFloorIndicator;
    private GameObject currentArrow;
    private Coroutine guidanceCoroutine;
    private bool isGuiding = false;

    void Start()
    {
        InitializeGuidance();
    }

    /// <summary>
    /// 初始化引导系统
    /// </summary>
    private void InitializeGuidance()
    {
        // 初始化音频源
        if (audioSource == null)
        {
            audioSource = gameObject.AddComponent<AudioSource>();
        }
        audioSource.volume = voiceVolume;
        audioSource.spatialBlend = 0f; // 2D音频

        // 创建默认引导元素
        CreateDefaultGuidanceElements();

        Debug.Log("[VRUserGuidance] 用户引导系统初始化完成");
    }

    /// <summary>
    /// 创建默认引导元素
    /// </summary>
    private void CreateDefaultGuidanceElements()
    {
        // 如果没有提供预制体，创建简单的引导元素
        if (arrowPrefab == null)
        {
            arrowPrefab = CreateDefaultArrow();
        }

        if (floorIndicatorPrefab == null)
        {
            floorIndicatorPrefab = CreateDefaultFloorIndicator();
        }

        if (glowMaterial == null)
        {
            glowMaterial = CreateGlowMaterial();
        }
    }

    /// <summary>
    /// 创建默认箭头
    /// </summary>
    private GameObject CreateDefaultArrow()
    {
        GameObject arrow = GameObject.CreatePrimitive(PrimitiveType.Cylinder);
        arrow.name = "GuidanceArrow";
        arrow.transform.localScale = new Vector3(0.1f, 0.5f, 0.1f);
        
        // 添加材质
        Renderer renderer = arrow.GetComponent<Renderer>();
        Material arrowMaterial = new Material(Shader.Find("Standard"));
        arrowMaterial.color = guidanceColor;
        arrowMaterial.SetFloat("_Metallic", 0.5f);
        arrowMaterial.SetFloat("_Smoothness", 0.8f);
        renderer.material = arrowMaterial;

        // 移除碰撞器
        Collider collider = arrow.GetComponent<Collider>();
        if (collider != null) DestroyImmediate(collider);

        arrow.SetActive(false);
        return arrow;
    }

    /// <summary>
    /// 创建默认地面指示器
    /// </summary>
    private GameObject CreateDefaultFloorIndicator()
    {
        GameObject indicator = GameObject.CreatePrimitive(PrimitiveType.Cylinder);
        indicator.name = "FloorIndicator";
        indicator.transform.localScale = new Vector3(1f, 0.05f, 1f);
        
        // 添加材质
        Renderer renderer = indicator.GetComponent<Renderer>();
        Material indicatorMaterial = new Material(Shader.Find("Standard"));
        indicatorMaterial.color = new Color(guidanceColor.r, guidanceColor.g, guidanceColor.b, 0.7f);
        indicatorMaterial.SetFloat("_Mode", 3); // Transparent
        indicatorMaterial.SetInt("_SrcBlend", (int)UnityEngine.Rendering.BlendMode.SrcAlpha);
        indicatorMaterial.SetInt("_DstBlend", (int)UnityEngine.Rendering.BlendMode.OneMinusSrcAlpha);
        indicatorMaterial.SetInt("_ZWrite", 0);
        indicatorMaterial.EnableKeyword("_ALPHABLEND_ON");
        indicatorMaterial.renderQueue = 3000;
        renderer.material = indicatorMaterial;

        // 移除碰撞器
        Collider collider = indicator.GetComponent<Collider>();
        if (collider != null) DestroyImmediate(collider);

        indicator.SetActive(false);
        return indicator;
    }

    /// <summary>
    /// 创建发光材质
    /// </summary>
    private Material CreateGlowMaterial()
    {
        Material glow = new Material(Shader.Find("Standard"));
        glow.SetColor("_Color", Color.yellow);
        glow.SetColor("_EmissionColor", Color.yellow * 0.5f);
        glow.EnableKeyword("_EMISSION");
        glow.SetFloat("_Metallic", 0f);
        glow.SetFloat("_Smoothness", 0.9f);
        return glow;
    }

    /// <summary>
    /// 引导用户移动到最佳观看位置
    /// </summary>
    public IEnumerator GuideUserToOptimalPosition(Vector3 targetPosition, string guidanceMessage = "")
    {
        if (isGuiding)
        {
            StopCurrentGuidance();
        }

        isGuiding = true;

        // 显示引导消息
        if (!string.IsNullOrEmpty(guidanceMessage))
        {
            ShowGuidanceMessage(guidanceMessage);
        }

        // 创建地面指示器
        currentFloorIndicator = CreateFloorIndicator(targetPosition);

        // 创建方向箭头
        currentArrow = CreateDirectionArrow(targetPosition);

        // 播放语音引导
        if (enableVoiceGuidance)
        {
            PlayVoiceGuidance("请移动到绿色圆圈位置以获得最佳观看角度");
        }

        // 开始引导动画
        guidanceCoroutine = StartCoroutine(GuidanceAnimationCoroutine());

        // 等待用户移动到位置
        yield return StartCoroutine(WaitForUserPosition(targetPosition, guidanceDistance));

        // 清理引导元素
        StopCurrentGuidance();

        // 播放完成提示
        if (enableVoiceGuidance)
        {
            PlayVoiceGuidance("位置很好，现在可以清楚看到装配过程了");
        }

        isGuiding = false;
    }

    /// <summary>
    /// 创建地面指示器
    /// </summary>
    private GameObject CreateFloorIndicator(Vector3 position)
    {
        Vector3 floorPosition = new Vector3(position.x, guidanceHeight, position.z);
        GameObject indicator = Instantiate(floorIndicatorPrefab, floorPosition, Quaternion.identity);
        indicator.SetActive(true);
        return indicator;
    }

    /// <summary>
    /// 创建方向箭头
    /// </summary>
    private GameObject CreateDirectionArrow(Vector3 targetPosition)
    {
        Transform userTransform = Camera.main.transform;
        Vector3 userPosition = userTransform.position;
        
        // 计算箭头位置（用户和目标之间）
        Vector3 direction = (targetPosition - userPosition).normalized;
        Vector3 arrowPosition = userPosition + direction * 1f;
        arrowPosition.y = userPosition.y;

        // 创建箭头
        GameObject arrow = Instantiate(arrowPrefab, arrowPosition, Quaternion.identity);
        
        // 让箭头指向目标位置
        Vector3 lookDirection = targetPosition - arrowPosition;
        lookDirection.y = 0; // 保持水平
        if (lookDirection != Vector3.zero)
        {
            arrow.transform.rotation = Quaternion.LookRotation(lookDirection);
        }

        arrow.SetActive(true);
        return arrow;
    }

    /// <summary>
    /// 引导动画协程
    /// </summary>
    private IEnumerator GuidanceAnimationCoroutine()
    {
        while (isGuiding)
        {
            // 地面指示器脉冲效果
            if (currentFloorIndicator != null)
            {
                float pulse = Mathf.Sin(Time.time * pulseSpeed) * 0.5f + 0.5f;
                float scale = 1f + pulse * 0.3f;
                currentFloorIndicator.transform.localScale = new Vector3(scale, 0.05f, scale);
            }

            // 箭头上下浮动效果
            if (currentArrow != null)
            {
                float bob = Mathf.Sin(Time.time * pulseSpeed * 2f) * 0.1f;
                Vector3 originalPos = currentArrow.transform.position;
                originalPos.y += bob;
                currentArrow.transform.position = originalPos;
            }

            yield return null;
        }
    }

    /// <summary>
    /// 等待用户移动到指定位置
    /// </summary>
    private IEnumerator WaitForUserPosition(Vector3 targetPosition, float tolerance)
    {
        Transform userTransform = Camera.main.transform;
        
        while (isGuiding)
        {
            Vector3 userPosition = userTransform.position;
            float distance = Vector3.Distance(new Vector3(userPosition.x, 0, userPosition.z), 
                                            new Vector3(targetPosition.x, 0, targetPosition.z));
            
            if (distance <= tolerance)
            {
                break;
            }

            yield return new WaitForSeconds(0.1f);
        }
    }

    /// <summary>
    /// 高亮显示装配区域
    /// </summary>
    public void HighlightAssemblyArea(Transform assemblyArea, float duration = 3f)
    {
        if (assemblyArea == null) return;

        StartCoroutine(HighlightCoroutine(assemblyArea, duration));
    }

    /// <summary>
    /// 高亮协程
    /// </summary>
    private IEnumerator HighlightCoroutine(Transform assemblyArea, float duration)
    {
        // 获取所有渲染器
        Renderer[] renderers = assemblyArea.GetComponentsInChildren<Renderer>();
        Material[][] originalMaterials = new Material[renderers.Length][];

        // 保存原始材质并应用高亮
        for (int i = 0; i < renderers.Length; i++)
        {
            originalMaterials[i] = renderers[i].materials;
            
            Material[] highlightMaterials = new Material[originalMaterials[i].Length];
            for (int j = 0; j < highlightMaterials.Length; j++)
            {
                highlightMaterials[j] = glowMaterial;
            }
            renderers[i].materials = highlightMaterials;
        }

        // 等待指定时间
        yield return new WaitForSeconds(duration);

        // 恢复原始材质
        for (int i = 0; i < renderers.Length; i++)
        {
            if (renderers[i] != null)
            {
                renderers[i].materials = originalMaterials[i];
            }
        }
    }

    /// <summary>
    /// 显示引导消息
    /// </summary>
    private void ShowGuidanceMessage(string message)
    {
        if (guidanceText != null)
        {
            guidanceText.text = message;
            if (guidanceCanvas != null)
            {
                guidanceCanvas.gameObject.SetActive(true);
            }
        }
    }

    /// <summary>
    /// 隐藏引导消息
    /// </summary>
    private void HideGuidanceMessage()
    {
        if (guidanceCanvas != null)
        {
            guidanceCanvas.gameObject.SetActive(false);
        }
    }

    /// <summary>
    /// 播放语音引导
    /// </summary>
    public void PlayVoiceGuidance(string message)
    {
        if (!enableVoiceGuidance || audioSource == null) return;

        // 这里可以集成TTS系统
        // 暂时只输出日志
        Debug.Log($"[VRUserGuidance] 语音提示: {message}");
        
        // 如果有预录的音频文件，可以在这里播放
        // audioSource.PlayOneShot(audioClip);
    }

    /// <summary>
    /// 停止当前引导
    /// </summary>
    public void StopCurrentGuidance()
    {
        isGuiding = false;

        if (guidanceCoroutine != null)
        {
            StopCoroutine(guidanceCoroutine);
            guidanceCoroutine = null;
        }

        if (currentFloorIndicator != null)
        {
            DestroyImmediate(currentFloorIndicator);
            currentFloorIndicator = null;
        }

        if (currentArrow != null)
        {
            DestroyImmediate(currentArrow);
            currentArrow = null;
        }

        HideGuidanceMessage();
    }

    /// <summary>
    /// 公共方法：为装配步骤提供引导
    /// </summary>
    public void GuideForAssemblyStep(Vector3 optimalPosition, string stepDescription)
    {
        string message = $"下一步: {stepDescription}\n请移动到最佳观看位置";
        StartCoroutine(GuideUserToOptimalPosition(optimalPosition, message));
    }

    /// <summary>
    /// 公共方法：引导用户查看装配背面
    /// </summary>
    public void GuideToBackView(Transform assemblyArea)
    {
        Vector3 backPosition = assemblyArea.position - assemblyArea.forward * 2f;
        StartCoroutine(GuideUserToOptimalPosition(backPosition, "请移动到装配区域背面查看"));
    }

    void OnDestroy()
    {
        StopCurrentGuidance();
    }
}
