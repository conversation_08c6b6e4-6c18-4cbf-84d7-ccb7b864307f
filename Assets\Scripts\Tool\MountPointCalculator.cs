using UnityEngine;
using UnityEditor;
using System.Collections.Generic;

public class MountPointCalculator : EditorWindow
{
    [MenuItem("Tools/Calculate Mount Points")]
    static void OpenWindow()
    {
        GetWindow<MountPointCalculator>("Mount Point Calculator");
    }

    void OnGUI()
    {
        GUILayout.Label("Select parts to calculate mount points (Scale Adjusted)", EditorStyles.boldLabel);
        if (GUILayout.Button("Calculate for Selected Objects"))
        {
            CalculateMountPoints();
        }
    }

    void CalculateMountPoints()
    {
        List<string> output = new List<string>();
        foreach (GameObject obj in Selection.gameObjects)
        {
            Renderer renderer = obj.GetComponentInChildren<Renderer>();
            if (renderer == null)
            {
                Debug.LogWarning($"[MountPointCalculator] {obj.name} has no Renderer!");
                continue;
            }

            // 获取缩放因子
            Vector3 scale = obj.transform.localScale;
            if (scale == Vector3.zero)
            {
                Debug.LogWarning($"[MountPointCalculator] {obj.name} has zero scale!");
                continue;
            }

            // 获取边界框（世界坐标）
            Bounds bounds = renderer.bounds;
            Vector3 boundsMin = bounds.min;
            Vector3 boundsMax = bounds.max;
            Vector3 boundsCenter = bounds.center;

            // 转换为局部坐标（考虑Scale）
            Vector3 localMin = obj.transform.InverseTransformPoint(boundsMin); // 已自动处理Scale
            Vector3 localMax = obj.transform.InverseTransformPoint(boundsMax);
            Vector3 localCenter = obj.transform.InverseTransformPoint(boundsCenter);

            // 根据零件类型推算安装点
            Vector3 mountPoint;
            string partType = obj.name.Contains("M2X6") || obj.name.Contains("BA_5") ? "screw" :
                             obj.name.Contains("nut") ? "nut" :
                             obj.name.Contains("舵盘") ? "wheel" : "arm";

            switch (partType)
            {
                case "screw":
                    mountPoint = new Vector3(0, localMin.y, 0); // 螺丝底部
                    break;
                case "nut":
                    mountPoint = localCenter; // 螺母中心
                    break;
                case "wheel":
                    mountPoint = localCenter; // 轮子中心
                    break;
                case "arm":
                    mountPoint = new Vector3(0, localMax.y, 0); // 支架顶部（螺孔）
                    break;
                default:
                    mountPoint = localCenter;
                    break;
            }

            // 四舍五入到2位小数
            mountPoint = new Vector3(
                Mathf.Round(mountPoint.x * 100) / 100,
                Mathf.Round(mountPoint.y * 100) / 100,
                Mathf.Round(mountPoint.z * 100) / 100
            );

            // 输出Cypher格式
            string cypher = $"// {obj.name}\nmountPoint: [{mountPoint.x}, {mountPoint.y}, {mountPoint.z}]";
            output.Add(cypher);
            Debug.Log($"[MountPointCalculator] {obj.name}: {cypher} (Scale: {scale})");
        }

        // 保存到文件（可选）
        if (output.Count > 0)
        {
            string path = EditorUtility.SaveFilePanel("Save Mount Points", "", "mount_points.txt", "txt");
            if (!string.IsNullOrEmpty(path))
            {
                System.IO.File.WriteAllLines(path, output);
                Debug.Log($"[MountPointCalculator] Saved to {path}");
            }
        }
    }
}