using UnityEngine;
using System.Collections.Generic;
using System;
using SimpleJSON;

/// <summary>
/// 扁平图序列数据处理器
/// 专门处理外部系统发送的装配序列数据，并驱动动画系统执行
/// </summary>
public class SequenceDataProcessor : MonoBehaviour
{
    [Header("组件引用")]
    [SerializeField] private Neo4jAssemblyController assemblyController;
    [SerializeField] private AssemblyAnimationManager animationManager;
    
    [Header("序列数据设置")]
    [SerializeField] private TextAsset sequenceDataFile; // 用于测试的序列数据文件
    [SerializeField] private bool autoStartOnLoad = false; // 加载后自动开始
    [SerializeField] private float stepDelay = 1.0f; // 步骤间延迟
    
    [Header("调试设置")]
    [SerializeField] private bool enableDebugLog = true;
    [SerializeField] private bool showStepDetails = true;
    
    // 内部状态
    private List<SequenceStep> sequenceSteps = new List<SequenceStep>();
    private int currentStepIndex = 0;
    private bool isProcessing = false;
    
    /// <summary>
    /// 序列步骤数据结构
    /// </summary>
    [System.Serializable]
    public struct SequenceStep
    {
        public string movingPartName;
        public string targetPartName;
        public string movingPartRefPoint;
        public string targetPartRefPoint;
        public string connectionType;
        public string fastenerType;
        public string screwType;
        public string nutType;
        public List<string> additionalMountPoints;
        
        public override string ToString()
        {
            return $"{movingPartName}[{movingPartRefPoint}] -> {targetPartName}[{targetPartRefPoint}] ({connectionType})";
        }
    }
    
    void Start()
    {
        InitializeProcessor();
    }
    
    /// <summary>
    /// 初始化处理器
    /// </summary>
    private void InitializeProcessor()
    {
        // 自动查找组件
        if (assemblyController == null)
            assemblyController = FindObjectOfType<Neo4jAssemblyController>();
            
        if (animationManager == null)
            animationManager = FindObjectOfType<AssemblyAnimationManager>();
        
        // 验证组件
        if (assemblyController == null)
        {
            Debug.LogError("[SequenceDataProcessor] 未找到Neo4jAssemblyController组件！");
            enabled = false;
            return;
        }
        
        if (animationManager == null)
        {
            Debug.LogError("[SequenceDataProcessor] 未找到AssemblyAnimationManager组件！");
            enabled = false;
            return;
        }
        
        LogDebug("序列数据处理器初始化完成");
        
        // 如果有测试数据文件，自动加载
        if (sequenceDataFile != null)
        {
            LoadSequenceFromFile(sequenceDataFile);
        }
    }
    
    /// <summary>
    /// 从外部接收序列数据
    /// </summary>
    /// <param name="jsonData">JSON格式的序列数据</param>
    public void ReceiveSequenceData(string jsonData)
    {
        LogDebug("接收外部序列数据");
        
        try
        {
            ParseSequenceData(jsonData);
            
            if (autoStartOnLoad && sequenceSteps.Count > 0)
            {
                StartSequenceExecution();
            }
        }
        catch (System.Exception e)
        {
            Debug.LogError($"[SequenceDataProcessor] 解析序列数据失败: {e.Message}");
        }
    }
    
    /// <summary>
    /// 从文件加载序列数据
    /// </summary>
    /// <param name="dataFile">数据文件</param>
    public void LoadSequenceFromFile(TextAsset dataFile)
    {
        if (dataFile == null)
        {
            Debug.LogWarning("[SequenceDataProcessor] 数据文件为空");
            return;
        }
        
        LogDebug($"从文件加载序列数据: {dataFile.name}");
        ReceiveSequenceData(dataFile.text);
    }
    
    /// <summary>
    /// 解析序列数据
    /// </summary>
    /// <param name="jsonData">JSON数据</param>
    private void ParseSequenceData(string jsonData)
    {
        sequenceSteps.Clear();
        
        var jsonObject = JSON.Parse(jsonData);
        var sequenceArray = jsonObject["sequence"];
        
        if (sequenceArray == null)
        {
            Debug.LogError("[SequenceDataProcessor] JSON数据中未找到'sequence'字段");
            return;
        }
        
        foreach (JSONNode stepNode in sequenceArray.AsArray)
        {
            var step = new SequenceStep
            {
                movingPartName = stepNode["movingPartName"],
                targetPartName = stepNode["targetPartName"],
                movingPartRefPoint = stepNode["movingPartRefPoint"],
                targetPartRefPoint = stepNode["targetPartRefPoint"],
                connectionType = stepNode["connectionType"],
                fastenerType = stepNode["fastenerType"],
                screwType = stepNode["screwType"],
                nutType = stepNode["nutType"],
                additionalMountPoints = new List<string>()
            };
            
            // 解析额外装配点
            if (stepNode["additionalMountPoints"] != null)
            {
                foreach (JSONNode mountPoint in stepNode["additionalMountPoints"].AsArray)
                {
                    step.additionalMountPoints.Add(mountPoint.Value);
                }
            }
            
            sequenceSteps.Add(step);
        }
        
        LogDebug($"解析完成，共{sequenceSteps.Count}个装配步骤");
        
        if (showStepDetails)
        {
            for (int i = 0; i < sequenceSteps.Count; i++)
            {
                LogDebug($"步骤{i + 1}: {sequenceSteps[i]}");
            }
        }
    }
    
    /// <summary>
    /// 开始执行序列
    /// </summary>
    public void StartSequenceExecution()
    {
        if (sequenceSteps.Count == 0)
        {
            Debug.LogWarning("[SequenceDataProcessor] 没有可执行的序列步骤");
            return;
        }
        
        if (isProcessing)
        {
            Debug.LogWarning("[SequenceDataProcessor] 序列正在执行中");
            return;
        }
        
        LogDebug("开始执行装配序列");
        currentStepIndex = 0;
        isProcessing = true;
        
        // 转换为系统内部格式并发送给装配控制器
        string internalFormatData = ConvertToInternalFormat();
        assemblyController.ReceiveExternalAssemblyData("SequenceAssembly", internalFormatData);
    }
    
    /// <summary>
    /// 转换为系统内部格式
    /// </summary>
    /// <returns>内部格式的JSON数据</returns>
    private string ConvertToInternalFormat()
    {
        var stepsArray = new JSONArray();
        
        foreach (var sequenceStep in sequenceSteps)
        {
            var step = new JSONObject();
            
            step["movingPart"] = sequenceStep.movingPartName;
            step["targetPart"] = sequenceStep.targetPartName;
            step["movingRefPoint"] = sequenceStep.movingPartRefPoint;
            step["targetRefPoint"] = sequenceStep.targetPartRefPoint;
            step["connectionType"] = sequenceStep.connectionType;
            
            // 紧固件信息
            var fastener = new JSONObject();
            fastener["type"] = sequenceStep.fastenerType;
            fastener["screwType"] = sequenceStep.screwType;
            fastener["nutType"] = sequenceStep.nutType;
            step["fastener"] = fastener;
            
            // 额外装配点
            var additionalMountPoints = new JSONArray();
            foreach (string mountPointStr in sequenceStep.additionalMountPoints)
            {
                // 解析格式: "主机架反_P5,安装连接块反2_P0,SCREW,SCREW_NUT,M3X10-10,Nut"
                string[] parts = mountPointStr.Split(',');
                if (parts.Length >= 6)
                {
                    var mountPoint = new JSONObject();
                    mountPoint["partName"] = parts[0];
                    mountPoint["mountPoint"] = parts[1];
                    mountPoint["screwType"] = parts[4];
                    mountPoint["nutType"] = parts[5];
                    additionalMountPoints.Add(mountPoint);
                }
            }
            step["additionalMountPoints"] = additionalMountPoints;
            
            stepsArray.Add(step);
        }
        
        var result = new JSONObject();
        result["steps"] = stepsArray;
        
        return result.ToString();
    }
    
    /// <summary>
    /// 停止序列执行
    /// </summary>
    public void StopSequenceExecution()
    {
        LogDebug("停止序列执行");
        isProcessing = false;
        currentStepIndex = 0;
    }
    
    /// <summary>
    /// 重置序列
    /// </summary>
    public void ResetSequence()
    {
        LogDebug("重置序列");
        StopSequenceExecution();
        
        if (assemblyController != null)
        {
            assemblyController.ResetAssembly();
        }
    }
    
    /// <summary>
    /// 获取序列状态信息
    /// </summary>
    /// <returns>状态信息</returns>
    public string GetSequenceStatus()
    {
        if (sequenceSteps.Count == 0)
            return "无序列数据";
            
        if (isProcessing)
            return $"执行中 ({currentStepIndex + 1}/{sequenceSteps.Count})";
            
        return $"就绪 ({sequenceSteps.Count}个步骤)";
    }
    
    /// <summary>
    /// 调试日志输出
    /// </summary>
    /// <param name="message">日志消息</param>
    private void LogDebug(string message)
    {
        if (enableDebugLog)
        {
            Debug.Log($"[SequenceDataProcessor] {message}");
        }
    }
    
    // 测试方法
    [ContextMenu("加载测试序列")]
    public void LoadTestSequence()
    {
        if (sequenceDataFile != null)
        {
            LoadSequenceFromFile(sequenceDataFile);
        }
        else
        {
            Debug.LogWarning("[SequenceDataProcessor] 测试序列文件未配置");
        }
    }
    
    [ContextMenu("开始执行序列")]
    public void StartTestExecution()
    {
        StartSequenceExecution();
    }
    
    [ContextMenu("重置序列")]
    public void ResetTestSequence()
    {
        ResetSequence();
    }
}
