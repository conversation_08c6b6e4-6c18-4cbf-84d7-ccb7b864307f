using UnityEngine;
using UnityEditor;
using System.IO;
using System.Collections.Generic; // For List<GameObject>

public class FBXConverterTool : EditorWindow
{
    // --- 定义文件处理模式的枚举 ---
    private enum FileOverwriteMode
    {
        AskPerFile,     // 逐个询问
        OverwriteAll,   // 覆盖所有
        SkipAll         // 跳过所有
    }

    // --- 定义源文件选择模式的枚举 ---
    private enum SourceMode
    {
        Folder,         // 从文件夹加载
        SingleFile      // 选择单个FBX文件
    }

    // --- 工具界面状态变量 ---
    private SourceMode currentSourceMode = SourceMode.Folder; // 默认从文件夹加载
    private string sourceFolderPath = "Assets/Resources/robotic arms Middle"; // 默认FBX文件夹路径
    private Object singleFbxFile; // 用户选择的单个FBX文件
    private string targetPrefabFolder = "Assets/Prefabs/10scale"; // 默认Prefab保存路径

    // --- 菜单项：打开工具窗口 ---
    [MenuItem("Tools/FBX 批量转换 Prefab 高级版")]
    public static void ShowWindow()
    {
        GetWindow<FBXConverterTool>("FBX 转换 Prefab");
    }

    // --- 绘制工具窗口 UI ---
    void OnGUI()
    {
        GUILayout.Label("FBX 模型转换为 Prefab 工具", EditorStyles.boldLabel);
        EditorGUILayout.Space();

        // --- 源文件选择模式 ---
        GUILayout.Label("选择源 FBX 模式:", EditorStyles.miniLabel);
        currentSourceMode = (SourceMode)EditorGUILayout.EnumPopup("源模式", currentSourceMode);

        if (currentSourceMode == SourceMode.Folder)
        {
            // --- 文件夹模式 UI ---
            EditorGUILayout.HelpBox("请确保你的 FBX 文件在 'Assets/Resources/' 目录下的某个子文件夹中，并输入该子文件夹的相对路径（例如: robotic arms Middle）。", MessageType.Info);
            sourceFolderPath = EditorGUILayout.TextField("Resources 子路径", sourceFolderPath);

            // 提示用户如果文件夹不存在
            if (!AssetDatabase.IsValidFolder("Assets/Resources/" + sourceFolderPath))
            {
                EditorGUILayout.HelpBox($"警告: 路径 'Assets/Resources/{sourceFolderPath}' 不存在或不是有效的 Resources 文件夹。", MessageType.Warning);
            }
        }
        else // SingleFile 模式
        {
            // --- 单文件模式 UI ---
            singleFbxFile = EditorGUILayout.ObjectField("选择 FBX 文件", singleFbxFile, typeof(GameObject), false);
            if (singleFbxFile != null && !(singleFbxFile is GameObject) || (singleFbxFile is GameObject go && !AssetDatabase.GetAssetPath(go).EndsWith(".fbx", System.StringComparison.OrdinalIgnoreCase)))
            {
                EditorGUILayout.HelpBox("请确保选择的是一个有效的 FBX 模型文件。", MessageType.Error);
                singleFbxFile = null; // 清除无效选择
            }
        }

        EditorGUILayout.Space();

        // --- 目标路径选择 ---
        GUILayout.Label("选择 Prefab 保存路径:", EditorStyles.miniLabel);
        EditorGUILayout.BeginHorizontal();
        targetPrefabFolder = EditorGUILayout.TextField("目标路径 (Assets/)", targetPrefabFolder);
        if (GUILayout.Button("选择文件夹", GUILayout.Width(100)))
        {
            string selectedPath = EditorUtility.OpenFolderPanel("选择 Prefab 保存文件夹", Application.dataPath, "");
            if (!string.IsNullOrEmpty(selectedPath))
            {
                // 将完整路径转换为 Assets 相对路径
                if (selectedPath.StartsWith(Application.dataPath))
                {
                    targetPrefabFolder = "Assets" + selectedPath.Substring(Application.dataPath.Length);
                }
                else
                {
                    EditorUtility.DisplayDialog("路径错误", "请选择项目 'Assets/' 文件夹内的路径。", "确定");
                }
            }
        }
        EditorGUILayout.EndHorizontal();
        if (!AssetDatabase.IsValidFolder(targetPrefabFolder))
        {
            EditorGUILayout.HelpBox("警告: 目标路径不存在，脚本将尝试自动创建。", MessageType.Warning);
        }

        EditorGUILayout.Space();

        // --- 执行按钮 ---
        if (GUILayout.Button("开始转换 FBX 为 Prefab", GUILayout.Height(30)))
        {
            // 输入验证
            if (currentSourceMode == SourceMode.Folder)
            {
                if (string.IsNullOrEmpty(sourceFolderPath) || !AssetDatabase.IsValidFolder("Assets/Resources/" + sourceFolderPath))
                {
                    EditorUtility.DisplayDialog("错误", "请检查 'Resources 子路径' 是否有效。", "确定");
                    return;
                }
            }
            else // SingleFile 模式
            {
                if (singleFbxFile == null)
                {
                    EditorUtility.DisplayDialog("错误", "请在 '选择 FBX 文件' 字段中选择一个 FBX 文件。", "确定");
                    return;
                }
            }

            if (string.IsNullOrEmpty(targetPrefabFolder))
            {
                EditorUtility.DisplayDialog("错误", "请指定 Prefab 文件的保存路径。", "确定");
                return;
            }

            ProcessFBXToPrefabs();
        }
    }

    // --- 核心转换逻辑 ---
    private void ProcessFBXToPrefabs()
    {
        List<GameObject> fbxModelsToProcess = new List<GameObject>();

        // 根据选择的模式加载 FBX 文件
        if (currentSourceMode == SourceMode.Folder)
        {
            string fullResourcesPath = "Assets/Resources/" + sourceFolderPath;
            if (!AssetDatabase.IsValidFolder(fullResourcesPath))
            {
                Debug.LogError($"指定的 Resources 路径不存在: {fullResourcesPath}");
                EditorUtility.DisplayDialog("错误", $"指定的 Resources 路径不存在: {fullResourcesPath}", "确定");
                return;
            }

            // Resources.LoadAll 的路径不包含 "Assets/Resources/"
            GameObject[] loadedModels = Resources.LoadAll<GameObject>(sourceFolderPath);
            if (loadedModels.Length == 0)
            {
                Debug.LogError($"在 `Resources/{sourceFolderPath}` 目录下没有找到 FBX 资源或GameObject。");
                EditorUtility.DisplayDialog("提示", $"在 `Resources/{sourceFolderPath}` 目录下没有找到 FBX 资源或GameObject。", "确定");
                return;
            }
            fbxModelsToProcess.AddRange(loadedModels);
        }
        else // SingleFile 模式
        {
            if (singleFbxFile != null)
            {
                fbxModelsToProcess.Add(singleFbxFile as GameObject);
            }
            else
            {
                Debug.LogError("未选择有效的单个 FBX 文件。");
                EditorUtility.DisplayDialog("错误", "未选择有效的单个 FBX 文件。", "确定");
                return;
            }
        }

        if (fbxModelsToProcess.Count == 0)
        {
            EditorUtility.DisplayDialog("提示", "没有找到要处理的 FBX 文件。", "确定");
            return;
        }

        // 确保目标 Prefab 目录存在
        CreateTargetFolder(targetPrefabFolder);

        // --- 核心改进：引入文件覆盖模式状态 ---
        FileOverwriteMode currentOverwriteMode = FileOverwriteMode.AskPerFile; // 默认逐个询问

        // 启用批处理模式，提高性能并避免不必要的资产刷新
        AssetDatabase.StartAssetEditing();

        int processedCount = 0;
        int totalToProcess = fbxModelsToProcess.Count;
        try
        {
            for (int i = 0; i < totalToProcess; i++)
            {
                GameObject model = fbxModelsToProcess[i];
                string prefabPath = $"{targetPrefabFolder}/{model.name}.prefab";

                // 显示进度条
                EditorUtility.DisplayProgressBar(
                    "转换 FBX 为 Prefab",
                    $"正在处理 '{model.name}' ({i + 1}/{totalToProcess})...",
                    (float)i / totalToProcess
                );

                // 如果文件已存在
                if (File.Exists(prefabPath))
                {
                    // 根据当前模式决定行为
                    if (currentOverwriteMode == FileOverwriteMode.SkipAll)
                    {
                        Debug.LogWarning($"跳过创建已存在的 Prefab (全局设置: 跳过所有): {prefabPath}");
                        continue; // 跳过当前模型
                    }
                    else if (currentOverwriteMode == FileOverwriteMode.OverwriteAll)
                    {
                        // 继续执行下面的覆盖逻辑，不再询问
                        Debug.Log($"覆盖已存在的 Prefab (全局设置: 覆盖所有): {prefabPath}");
                    }
                    else // currentOverwriteMode == FileOverwriteMode.AskPerFile (逐个询问)
                    {
                        // 弹出对话框询问用户处理方式
                        int choice = EditorUtility.DisplayDialogComplex(
                            "Prefab 已存在",
                            $"路径 '{prefabPath}' 下的 Prefab 已经存在。\n\n你想要怎么处理？",
                            "覆盖当前",         // 选项 0: 覆盖当前文件
                            "跳过当前",         // 选项 1: 跳过当前文件
                            "取消操作"          // 选项 2: 取消所有操作
                        );

                        if (choice == 0) // 覆盖当前
                        {
                            // 额外询问是否应用于所有后续文件
                            bool applyToAll = EditorUtility.DisplayDialog(
                                "应用于所有？",
                                $"你选择了覆盖 '{model.name}.prefab'。\n是否将 '覆盖' 应用于所有后续冲突文件？",
                                "应用到所有",
                                "仅当前"
                            );
                            if (applyToAll)
                            {
                                currentOverwriteMode = FileOverwriteMode.OverwriteAll;
                                Debug.Log("用户选择：后续所有冲突文件都覆盖。");
                            }
                        }
                        else if (choice == 1) // 跳过当前
                        {
                            // 额外询问是否应用于所有后续文件
                            bool applyToAll = EditorUtility.DisplayDialog(
                                "应用于所有？",
                                $"你选择了跳过 '{model.name}.prefab'。\n是否将 '跳过' 应用于所有后续冲突文件？",
                                "应用到所有",
                                "仅当前"
                            );
                            if (applyToAll)
                            {
                                currentOverwriteMode = FileOverwriteMode.SkipAll;
                                Debug.Log("用户选择：后续所有冲突文件都跳过。");
                            }
                            Debug.LogWarning($"跳过创建已存在的 Prefab: {prefabPath}");
                            continue; // 跳过当前模型，处理下一个
                        }
                        else if (choice == 2) // 取消操作
                        {
                            Debug.Log("用户取消操作。");
                            return; // 终止整个方法
                        }
                    }
                }

                // 如果代码执行到这里，表示可以创建或覆盖当前的 Prefab
                GameObject instance = Instantiate(model);
                instance.name = model.name;

                // 添加碰撞体组件
                MeshFilter meshFilter = instance.GetComponentInChildren<MeshFilter>();
                if (meshFilter != null)
                {
                    MeshCollider meshCollider = instance.AddComponent<MeshCollider>();
                    meshCollider.sharedMesh = meshFilter.sharedMesh;
                    meshCollider.convex = true;
                }
                else
                {
                    instance.AddComponent<BoxCollider>();
                }

                // 保存为预制体
                PrefabUtility.SaveAsPrefabAsset(instance, prefabPath);
                DestroyImmediate(instance); // 删除场景中的临时实例

                Debug.Log($"已创建预制体: {prefabPath}");
                processedCount++;
            }
        }
        finally
        {
            // 确保无论如何都停止批处理和清除进度条
            AssetDatabase.StopAssetEditing();
            EditorUtility.ClearProgressBar();
        }

        AssetDatabase.Refresh();
        EditorUtility.DisplayDialog("完成", $"所有 FBX 资源已转换为预制体！共处理 {processedCount} 个文件。", "确定");
    }

    // --- 辅助方法：确保目标文件夹存在 ---
    private void CreateTargetFolder(string path)
    {
        if (string.IsNullOrEmpty(path)) return;

        // 移除 "Assets/" 前缀以便 Path.GetDirectoryName 正确工作
        string relativePath = path;
        if (path.StartsWith("Assets/"))
        {
            relativePath = path.Substring("Assets/".Length);
        }

        Stack<string> foldersToCreate = new Stack<string>();
        string currentPath = "Assets";
        string[] parts = relativePath.Split('/');

        foreach (string part in parts)
        {
            string fullPath = Path.Combine(currentPath, part).Replace("\\", "/");
            if (!AssetDatabase.IsValidFolder(fullPath))
            {
                foldersToCreate.Push(part);
            }
            currentPath = fullPath;
        }

        // 从内到外创建文件夹
        string basePath = Path.GetDirectoryName(currentPath).Replace("\\", "/");
        if (!AssetDatabase.IsValidFolder(basePath))
        {
            // 如果父路径本身不存在，可能需要递归创建，但 AssetDatabase.CreateFolder 只能创建一层
            // 简单起见，这里假设父路径是存在的，或者会在下一层循环中创建
            Debug.LogWarning($"基路径 '{basePath}' 不存在，可能会导致创建失败。");
        }

        while (foldersToCreate.Count > 0)
        {
            string folderName = foldersToCreate.Pop();
            string parentFolder = Path.GetDirectoryName(currentPath).Replace("\\", "/");

            // 获取当前要创建的文件夹的完整路径
            string folderToCreateFullPath = Path.Combine(parentFolder, folderName).Replace("\\", "/");

            if (!AssetDatabase.IsValidFolder(folderToCreateFullPath))
            {
                // 从当前路径的父级开始创建
                string parentDirForCreate = Path.GetDirectoryName(folderToCreateFullPath).Replace("\\", "/");
                string actualFolderName = Path.GetFileName(folderToCreateFullPath);
                AssetDatabase.CreateFolder(parentDirForCreate, actualFolderName);
                Debug.Log($"创建文件夹: {folderToCreateFullPath}");
            }
            currentPath = folderToCreateFullPath; // 更新当前路径为刚创建的文件夹
        }
    }
}