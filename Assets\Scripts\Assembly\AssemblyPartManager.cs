using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using TMPro;

/// <summary>
/// 装配零件管理器
/// 控制零件的显示/隐藏，并提供装配步骤提示
/// </summary>
public class AssemblyPartManager : MonoBehaviour
{
    [Header("UI设置")]
    [SerializeField] private Text stepInfoText;
    [Tooltip("显示装配步骤信息的文本框（Legacy UI Text）")]

    [SerializeField] private TextMeshProUGUI stepInfoTextTMP;
    [Tooltip("显示装配步骤信息的文本框（TextMeshPro UGUI）")]
    
    [Header("零件管理设置")]
    [SerializeField] private string[] alwaysVisibleParts = { "中下段" };
    [Tooltip("始终显示的零件名称（如中下段）")]

    [SerializeField] private bool hidePartsOnStart = true;
    [Tooltip("启动时是否隐藏零件")]

    [Header("紧固件管理设置")]
    [SerializeField] private Transform screwsContainer;
    [Tooltip("场景中螺丝的容器")]

    [SerializeField] private Transform nutsContainer;
    [<PERSON>lt<PERSON>("场景中螺母的容器")]

    [SerializeField] private bool manageFasteners = true;
    [Tooltip("是否管理螺丝和螺母的显示/隐藏")]
    
    [Header("调试设置")]
    [SerializeField] private bool debugMode = true;
    [SerializeField] private bool showDetailedLogs = false;
    
    // 零件状态管理
    private Dictionary<string, GameObject> allParts = new Dictionary<string, GameObject>();
    private HashSet<string> visibleParts = new HashSet<string>();
    private Dictionary<string, bool> originalActiveStates = new Dictionary<string, bool>();

    // 紧固件状态管理
    private Dictionary<string, GameObject> allFasteners = new Dictionary<string, GameObject>();
    private Dictionary<string, bool> fastenerOriginalStates = new Dictionary<string, bool>();
    
    // 当前装配步骤信息
    private string currentStepInfo = "";
    private List<string> currentStepParts = new List<string>();
    
    // 事件
    public System.Action<string> OnPartShown;
    public System.Action<string> OnPartHidden;
    public System.Action<string> OnStepInfoUpdated;
    
    void Start()
    {
        InitializePartManager();
        
        if (hidePartsOnStart)
        {
            HideAllPartsExceptAlwaysVisible();
        }
        
        UpdateStepInfoDisplay("准备开始装配...");
        
        if (debugMode)
        {
            Debug.Log($"🔧 装配零件管理器已初始化，管理 {allParts.Count} 个零件");
        }
    }
    
    /// <summary>
    /// 初始化零件管理器
    /// </summary>
    private void InitializePartManager()
    {
        allParts.Clear();
        originalActiveStates.Clear();
        visibleParts.Clear();
        
        // 查找所有AssemblyPart组件
        AssemblyPart[] assemblyParts = FindObjectsOfType<AssemblyPart>();
        
        foreach (AssemblyPart part in assemblyParts)
        {
            string partName = part.PartName;
            GameObject partObject = part.gameObject;
            
            allParts[partName] = partObject;
            originalActiveStates[partName] = partObject.activeSelf;
            
            // 检查是否是始终显示的零件
            if (IsAlwaysVisiblePart(partName))
            {
                visibleParts.Add(partName);
                if (showDetailedLogs)
                {
                    Debug.Log($"📌 始终显示零件: {partName}");
                }
            }
            
            if (showDetailedLogs)
            {
                Debug.Log($"📝 注册零件: {partName} (原始状态: {partObject.activeSelf})");
            }
        }

        // 初始化紧固件管理
        if (manageFasteners)
        {
            InitializeFasteners();
        }

        Debug.Log($"✅ 零件管理器初始化完成，共管理 {allParts.Count} 个零件，{allFasteners.Count} 个紧固件");
    }

    /// <summary>
    /// 初始化紧固件管理
    /// </summary>
    private void InitializeFasteners()
    {
        allFasteners.Clear();
        fastenerOriginalStates.Clear();

        // 初始化螺丝
        if (screwsContainer != null)
        {
            foreach (Transform child in screwsContainer)
            {
                string fastenerName = child.name;
                allFasteners[fastenerName] = child.gameObject;
                fastenerOriginalStates[fastenerName] = child.gameObject.activeSelf;

                if (showDetailedLogs)
                {
                    Debug.Log($"🔩 注册螺丝: {fastenerName} (原始状态: {child.gameObject.activeSelf})");
                }
            }
        }

        // 初始化螺母
        if (nutsContainer != null)
        {
            foreach (Transform child in nutsContainer)
            {
                string fastenerName = child.name;
                allFasteners[fastenerName] = child.gameObject;
                fastenerOriginalStates[fastenerName] = child.gameObject.activeSelf;

                if (showDetailedLogs)
                {
                    Debug.Log($"🔧 注册螺母: {fastenerName} (原始状态: {child.gameObject.activeSelf})");
                }
            }
        }

        Debug.Log($"🔩 紧固件初始化完成，共管理 {allFasteners.Count} 个紧固件");
    }

    /// <summary>
    /// 隐藏所有零件（除了始终显示的零件）
    /// </summary>
    public void HideAllPartsExceptAlwaysVisible()
    {
        int hiddenCount = 0;
        
        foreach (var kvp in allParts)
        {
            string partName = kvp.Key;
            GameObject partObject = kvp.Value;
            
            if (!IsAlwaysVisiblePart(partName))
            {
                partObject.SetActive(false);
                visibleParts.Remove(partName);
                hiddenCount++;
                
                if (showDetailedLogs)
                {
                    Debug.Log($"🙈 隐藏零件: {partName}");
                }
            }
        }

        // 隐藏所有紧固件
        if (manageFasteners)
        {
            HideAllFasteners();
        }

        if (debugMode)
        {
            Debug.Log($"🙈 隐藏了 {hiddenCount} 个零件，保留 {visibleParts.Count} 个始终显示的零件");
        }
    }

    /// <summary>
    /// 隐藏所有紧固件
    /// </summary>
    private void HideAllFasteners()
    {
        int hiddenCount = 0;

        foreach (var kvp in allFasteners)
        {
            string fastenerName = kvp.Key;
            GameObject fastenerObject = kvp.Value;

            if (fastenerObject.activeSelf)
            {
                fastenerObject.SetActive(false);
                hiddenCount++;

                if (showDetailedLogs)
                {
                    Debug.Log($"🔩 隐藏紧固件: {fastenerName}");
                }
            }
        }

        if (debugMode)
        {
            Debug.Log($"🔩 隐藏了 {hiddenCount} 个紧固件");
        }
    }

    /// <summary>
    /// 显示指定紧固件
    /// </summary>
    /// <param name="fastenerName">紧固件名称</param>
    public void ShowFastener(string fastenerName)
    {
        if (allFasteners.ContainsKey(fastenerName))
        {
            GameObject fastenerObject = allFasteners[fastenerName];

            if (!fastenerObject.activeSelf)
            {
                fastenerObject.SetActive(true);

                if (debugMode)
                {
                    Debug.Log($"🔩 显示紧固件: {fastenerName}");
                }
            }
        }
        else
        {
            Debug.LogWarning($"⚠️ 未找到紧固件: {fastenerName}");
            if (debugMode && showDetailedLogs)
            {
                Debug.Log($"📋 当前管理的紧固件: {string.Join(", ", allFasteners.Keys)}");
            }
        }
    }

    /// <summary>
    /// 根据类型显示紧固件（支持模糊匹配）
    /// </summary>
    /// <param name="fastenerType">紧固件类型（如M2X6、BA_5等）</param>
    /// <param name="count">需要显示的数量</param>
    public void ShowFastenersByType(string fastenerType, int count = 1)
    {
        if (!manageFasteners) return;

        var matchingFasteners = new List<string>();

        foreach (var kvp in allFasteners)
        {
            string fastenerName = kvp.Key;
            GameObject fastenerObject = kvp.Value;

            // 检查名称是否包含类型，且当前是隐藏状态
            if (fastenerName.Contains(fastenerType) && !fastenerObject.activeSelf)
            {
                matchingFasteners.Add(fastenerName);
            }
        }

        // 显示指定数量的紧固件
        int showCount = Mathf.Min(count, matchingFasteners.Count);
        for (int i = 0; i < showCount; i++)
        {
            ShowFastener(matchingFasteners[i]);
        }

        if (debugMode)
        {
            Debug.Log($"🔩 根据类型 {fastenerType} 显示了 {showCount} 个紧固件");
        }
    }

    /// <summary>
    /// 显示指定零件
    /// </summary>
    /// <param name="partName">零件名称</param>
    public void ShowPart(string partName)
    {
        if (allParts.ContainsKey(partName))
        {
            GameObject partObject = allParts[partName];
            
            if (!partObject.activeSelf)
            {
                partObject.SetActive(true);
                visibleParts.Add(partName);
                
                OnPartShown?.Invoke(partName);
                
                if (debugMode)
                {
                    Debug.Log($"👁️ 显示零件: {partName}");
                }
            }
        }
        else
        {
            Debug.LogWarning($"⚠️ 未找到零件: {partName}");
            if (debugMode && showDetailedLogs)
            {
                Debug.Log($"📋 当前管理的零件: {string.Join(", ", allParts.Keys)}");
            }
        }
    }
    
    /// <summary>
    /// 隐藏指定零件（如果不是始终显示的零件）
    /// </summary>
    /// <param name="partName">零件名称</param>
    public void HidePart(string partName)
    {
        if (IsAlwaysVisiblePart(partName))
        {
            Debug.LogWarning($"⚠️ 无法隐藏始终显示的零件: {partName}");
            return;
        }
        
        if (allParts.ContainsKey(partName))
        {
            GameObject partObject = allParts[partName];
            
            if (partObject.activeSelf)
            {
                partObject.SetActive(false);
                visibleParts.Remove(partName);
                
                OnPartHidden?.Invoke(partName);
                
                if (debugMode)
                {
                    Debug.Log($"🙈 隐藏零件: {partName}");
                }
            }
        }
        else
        {
            Debug.LogWarning($"⚠️ 未找到零件: {partName}");
        }
    }
    
    /// <summary>
    /// 处理装配步骤，显示相关零件并更新提示信息
    /// </summary>
    /// <param name="movingPart">移动零件名称</param>
    /// <param name="targetPart">目标零件名称</param>
    /// <param name="fastenerType">紧固件类型</param>
    /// <param name="screwType">螺丝类型</param>
    /// <param name="nutType">螺母类型</param>
    public void ProcessAssemblyStep(string movingPart, string targetPart, 
        string fastenerType = "", string screwType = "", string nutType = "")
    {
        currentStepParts.Clear();
        
        // 显示相关零件
        ShowPart(movingPart);
        ShowPart(targetPart);

        currentStepParts.Add(movingPart);
        currentStepParts.Add(targetPart);

        // 显示相关紧固件
        if (manageFasteners && !string.IsNullOrEmpty(fastenerType) && fastenerType != "NONE")
        {
            if (!string.IsNullOrEmpty(screwType))
            {
                ShowFastenersByType(screwType, 1);
            }

            if (!string.IsNullOrEmpty(nutType))
            {
                ShowFastenersByType(nutType, 1);
            }
        }

        // 构建步骤信息
        string stepInfo = $"装配步骤:\n";
        stepInfo += $"• 移动零件: {movingPart}\n";
        stepInfo += $"• 目标零件: {targetPart}\n";

        // 添加紧固件信息
        if (!string.IsNullOrEmpty(fastenerType) && fastenerType != "NONE")
        {
            stepInfo += $"• 连接方式: {fastenerType}\n";

            if (!string.IsNullOrEmpty(screwType))
            {
                stepInfo += $"• 螺丝类型: {screwType}\n";
            }

            if (!string.IsNullOrEmpty(nutType))
            {
                stepInfo += $"• 螺母类型: {nutType}\n";
            }
        }
        
        currentStepInfo = stepInfo;
        UpdateStepInfoDisplay(currentStepInfo);
        
        if (debugMode)
        {
            Debug.Log($"🔧 处理装配步骤: {movingPart} -> {targetPart}");
            if (!string.IsNullOrEmpty(fastenerType) && fastenerType != "NONE")
            {
                Debug.Log($"   紧固件: {fastenerType}, 螺丝: {screwType}, 螺母: {nutType}");
            }
        }
    }
    
    /// <summary>
    /// 更新步骤信息显示
    /// </summary>
    /// <param name="info">要显示的信息</param>
    private void UpdateStepInfoDisplay(string info)
    {
        bool textUpdated = false;

        // 优先使用TextMeshPro
        if (stepInfoTextTMP != null)
        {
            stepInfoTextTMP.text = info;
            textUpdated = true;

            if (debugMode && showDetailedLogs)
            {
                Debug.Log($"📝 更新TMP文本显示: {info}");
                Debug.Log($"📝 TMP文本框状态: 激活={stepInfoTextTMP.gameObject.activeInHierarchy}, 启用={stepInfoTextTMP.enabled}");
            }
        }

        // 如果没有TMP，使用Legacy Text
        if (!textUpdated && stepInfoText != null)
        {
            stepInfoText.text = info;
            textUpdated = true;

            if (debugMode && showDetailedLogs)
            {
                Debug.Log($"📝 更新Legacy文本显示: {info}");
                Debug.Log($"📝 Legacy文本框状态: 激活={stepInfoText.gameObject.activeInHierarchy}, 启用={stepInfoText.enabled}");
            }
        }

        if (textUpdated)
        {
            OnStepInfoUpdated?.Invoke(info);
        }
        else
        {
            Debug.LogWarning("⚠️ 步骤信息文本框未设置！请设置 stepInfoTextTMP 或 stepInfoText");
        }
    }
    
    /// <summary>
    /// 检查是否是始终显示的零件
    /// </summary>
    /// <param name="partName">零件名称</param>
    /// <returns>是否始终显示</returns>
    private bool IsAlwaysVisiblePart(string partName)
    {
        foreach (string alwaysVisible in alwaysVisibleParts)
        {
            if (partName.Contains(alwaysVisible))
            {
                return true;
            }
        }
        return false;
    }
    
    /// <summary>
    /// 重置所有零件到原始状态
    /// </summary>
    public void ResetAllParts()
    {
        foreach (var kvp in allParts)
        {
            string partName = kvp.Key;
            GameObject partObject = kvp.Value;
            bool originalState = originalActiveStates[partName];
            
            partObject.SetActive(originalState);
            
            if (originalState)
            {
                visibleParts.Add(partName);
            }
            else
            {
                visibleParts.Remove(partName);
            }
        }
        
        UpdateStepInfoDisplay("已重置所有零件状态");
        
        if (debugMode)
        {
            Debug.Log("🔄 已重置所有零件到原始状态");
        }
    }
    
    /// <summary>
    /// 获取当前可见零件列表
    /// </summary>
    /// <returns>可见零件名称列表</returns>
    public List<string> GetVisibleParts()
    {
        return new List<string>(visibleParts);
    }
    
    /// <summary>
    /// 获取当前步骤涉及的零件
    /// </summary>
    /// <returns>当前步骤零件列表</returns>
    public List<string> GetCurrentStepParts()
    {
        return new List<string>(currentStepParts);
    }
    
    /// <summary>
    /// 设置步骤信息文本框
    /// </summary>
    /// <param name="textComponent">文本组件</param>
    public void SetStepInfoText(Text textComponent)
    {
        stepInfoText = textComponent;
        if (debugMode)
        {
            Debug.Log("📝 已设置步骤信息文本框");
        }
    }
    
    /// <summary>
    /// 获取零件管理状态信息
    /// </summary>
    /// <returns>状态信息字符串</returns>
    public string GetStatusInfo()
    {
        int visibleFasteners = 0;
        if (manageFasteners)
        {
            foreach (var kvp in allFasteners)
            {
                if (kvp.Value.activeSelf)
                {
                    visibleFasteners++;
                }
            }
        }

        return $"总零件数: {allParts.Count}\n" +
               $"可见零件数: {visibleParts.Count}\n" +
               $"始终显示零件数: {alwaysVisibleParts.Length}\n" +
               $"总紧固件数: {allFasteners.Count}\n" +
               $"可见紧固件数: {visibleFasteners}";
    }
}
