# 支持的紧固件类型

本文档列出了VR装配系统中支持的所有螺丝和螺母类型，以及它们对应的预制体名称。

## 🔩 螺丝类型 (Screw Types)

| 螺丝类型 | 预制体名称 | 描述 | 来源 |
|---------|-----------|------|------|
| `M2X6` | `ScrewM2X6` | M2×6规格螺丝 | 原有 |
| `BA_5` | `ScrewBA_5` | BA_5规格螺丝 | 原有 |
| `M3X10-10` | `ScrewM3X10-10` | M3×10-10规格螺丝 | 扁平图序列 |
| `M3X5-5` | `ScrewM3X5-5` | M3×5-5规格螺丝 | 扁平图序列 |
| `M3X16-16` | `ScrewM3X16-16` | M3×16-16规格螺丝 | 扁平图序列 |
| `ST2.9X6.5` | `ScrewST2.9X6.5` | ST2.9×6.5规格螺丝 | 扁平图序列 |
| `M3X8-6.35` | `ScrewM3X8-6.35` | M3×8-6.35规格螺丝 | 扁平图序列 |

## 🔧 螺母类型 (Nut Types)

| 螺母类型 | 预制体名称 | 描述 | 来源 |
|---------|-----------|------|------|
| `nut` | `Nut` | 通用螺母 | 原有 |
| `Nut` | `Nut` | 通用螺母（大写） | 扁平图序列 |
| `nut m3-c` | `NutM3-C` | M3-C规格螺母 | 扁平图序列 |
| `""` (空字符串) | `""` | 无螺母 | 系统 |
| `null` | `""` | 无螺母 | 系统 |

## 📋 使用统计

### 扁平图序列中的使用频率

#### 螺丝类型使用次数：
- `BA_5`: 2次
- `M3X10-10`: 2次  
- `M3X5-5`: 4次
- `M3X16-16`: 14次 (最常用)
- `ST2.9X6.5`: 1次
- `M3X8-6.35`: 4次

#### 螺母类型使用次数：
- 空字符串 `""`: 19次 (最常用，表示只用螺丝)
- `Nut`: 2次
- `nut m3-c`: 6次

## 🛠️ 预制体命名规则

### 螺丝预制体命名：
- 格式：`Screw{螺丝类型}`
- 示例：`ScrewM3X16-16`、`ScrewBA_5`

### 螺母预制体命名：
- 格式：`Nut{螺母规格}`
- 示例：`NutM3-C`、`Nut`

## 📁 场景中的组织结构

建议在场景中按以下结构组织紧固件：

```
Screws (容器)
├── ScrewM2X6_1
├── ScrewM2X6_2
├── ScrewBA_5_1
├── ScrewBA_5_2
├── ScrewM3X10-10_1
├── ScrewM3X10-10_2
├── ScrewM3X5-5_1
├── ScrewM3X5-5_2
├── ScrewM3X5-5_3
├── ScrewM3X5-5_4
├── ScrewM3X16-16_1
├── ScrewM3X16-16_2
├── ... (更多M3X16-16螺丝)
├── ScrewST2.9X6.5_1
├── ScrewM3X8-6.35_1
├── ScrewM3X8-6.35_2
├── ScrewM3X8-6.35_3
└── ScrewM3X8-6.35_4

Nuts (容器)
├── Nut_1
├── Nut_2
├── NutM3-C_1
├── NutM3-C_2
├── NutM3-C_3
├── NutM3-C_4
├── NutM3-C_5
└── NutM3-C_6
```

## ⚙️ 系统配置

### AssemblyPartManager 设置：
1. 将 `Screws` 容器拖拽到 `screwsContainer` 字段
2. 将 `Nuts` 容器拖拽到 `nutsContainer` 字段
3. 启用 `manageFasteners` 选项

### 紧固件命名要求：
- 螺丝名称必须包含类型信息（如名称中包含"M3X16-16"）
- 螺母名称必须包含类型信息（如名称中包含"M3-C"）
- 系统使用模糊匹配来查找合适的紧固件

## 🔍 调试信息

使用 `AssemblyDebugHelper` 可以查看：
- 当前场景中所有紧固件的状态
- 激活/非激活紧固件的数量
- 紧固件的名称和容器信息

按 **F1** 键可以查看详细的调试信息。

## 📝 注意事项

1. **预制体准备**：确保所有列出的预制体都已在项目中准备好
2. **命名一致性**：场景中的紧固件名称必须包含对应的类型信息
3. **数量规划**：根据装配序列的需求准备足够数量的各类型紧固件
4. **组件要求**：所有紧固件都应该有 `AssemblyPart` 组件
5. **容器设置**：正确设置螺丝和螺母的容器引用

## 🔄 扩展支持

如果需要添加新的螺丝或螺母类型：

1. 在 `Neo4jAssemblyController.cs` 的 `GetScrewPrefabName()` 和 `GetNutPrefabName()` 方法中添加新的case
2. 在 `AssemblyStepData.cs` 的对应方法中添加相同的case
3. 准备对应的预制体
4. 在场景中放置相应数量的紧固件
5. 更新本文档
