using System.IO;
using UnityEngine;

namespace Woz.Logging
{
    /// <summary>
    /// 将日志输出到文件
    /// </summary>
    public class FileAppender : ILogAppender
    {
        private readonly StreamWriter _writer;
        private readonly ILogFormatter _formatter;

        public FileAppender(string logFilePath, ILogFormatter formatter)
        {
            _formatter = formatter;
            try
            {
                // 确保目录存在
                Directory.CreateDirectory(Path.GetDirectoryName(logFilePath));
                
                // 使用FileStream并设置FileShare.ReadWrite以允许多个进程读取
                FileStream fileStream = new FileStream(logFilePath, FileMode.Append, FileAccess.Write, FileShare.ReadWrite);
                _writer = new StreamWriter(fileStream);
                _writer.AutoFlush = true; // 确保日志实时写入
            }
            catch (IOException e)
            {
                Debug.LogError($"[FileAppender] Failed to create or open log file at {logFilePath}: {e.Message}");
                _writer = null;
            }
        }

        public void Log(LogEntry entry)
        {
            if (_writer != null)
            {
                string message = _formatter.Format(entry);
                _writer.WriteLine(message);
            }
        }

        public void Dispose()
        {
            _writer?.Close();
            _writer?.Dispose();
        }
    }
} 