using UnityEngine;
using SimpleJSON;

/// <summary>
/// 简单装配桥接器
/// 作为一个中间件，监听来自EventManager的全局网络事件，
/// 并将这些事件转换为对现有 Neo4jAssemblyController 的调用。
/// 这使得我们无需修改核心的Neo4jAssemblyController代码，即可接入新的事件驱动系统。
/// </summary>
public class SimpleAssemblyBridge : MonoBehaviour
{
    private Neo4jAssemblyController _assemblyController;

    void Start()
    {
        // 获取对核心装配控制器的引用
        _assemblyController = FindObjectOfType<Neo4jAssemblyController>();
        if (_assemblyController == null)
        {
            Debug.LogError("[SimpleAssemblyBridge] 场景中未找到 Neo4jAssemblyController 实例！桥接器无法工作。");
            enabled = false;
            return;
        }

        // 注册对事件的监听
        RegisterEventListeners();
        Debug.Log("[SimpleAssemblyBridge] 初始化完成，已开始监听网络装配事件。");
    }

    private void OnDestroy()
    {
        // 在对象销毁时取消监听，防止内存泄漏
        UnregisterEventListeners();
    }

    private void RegisterEventListeners()
    {
        EventManager.OnAssemblyStepReceived += HandleAssemblyStep;
        // 在这里可以继续注册对其他事件的监听
        // EventManager.OnLoadSceneRequested += HandleLoadScene;
        // EventManager.OnSequenceCompleted += HandleSequenceCompleted;
    }

    private void UnregisterEventListeners()
    {
        EventManager.OnAssemblyStepReceived -= HandleAssemblyStep;
        // EventManager.OnLoadSceneRequested -= HandleLoadScene;
        // EventManager.OnSequenceCompleted -= HandleSequenceCompleted;
    }

    /// <summary>
    /// 处理接收到的单个装配步骤指令
    /// </summary>
    /// <param name="stepData">从服务器发来的单个步骤数据</param>
    private void HandleAssemblyStep(SequenceStepData stepData)
    {
        if (_assemblyController == null) return;

        Debug.Log($"[SimpleAssemblyBridge] 监听到装配步骤事件，正在处理: {stepData.movingPartName} -> {stepData.targetPartName}");

        // 1. 将单步数据转换为Neo4jAssemblyController期望的、包含"steps"数组的JSON格式
        string singleStepJson = ConvertStepToControllerFormat(stepData);

        // 2. 调用现有接口，将这个只包含一步的“序列”发送给控制器
        _assemblyController.ReceiveExternalAssemblyData($"NetworkStep_{stepData.stepIndex}", singleStepJson);

        // 3. 立即调用执行方法，处理队列中的这一个步骤
        _assemblyController.ExecuteNextStep();
    }

    /// <summary>
    /// 将单个SequenceStepData对象转换为Neo4jAssemblyController能够解析的JSON字符串。
    /// </summary>
    private string ConvertStepToControllerFormat(SequenceStepData step)
    {
        var stepsArray = new JSONArray();
        var stepObj = new JSONObject();

        stepObj["movingPart"] = step.movingPartName;
        stepObj["targetPart"] = step.targetPartName;
        stepObj["movingRefPoint"] = step.movingPartRefPoint;
        stepObj["targetRefPoint"] = step.targetPartRefPoint;
        stepObj["connectionType"] = step.connectionType;

        var fastener = new JSONObject();
        fastener["type"] = step.fastenerType;
        fastener["screwType"] = step.screwType;
        fastener["nutType"] = step.nutType;
        stepObj["fastener"] = fastener;
        
        // 修正：additionalMountPoints 是一个 string 列表, 需要解析
        var additionalMountPoints = new JSONArray();
        if (step.additionalMountPoints != null)
        {
            foreach (var mountPointStr in step.additionalMountPoints)
            {
                // 解析格式: "主机架反_P5,安装连接块反2_P0,SCREW,SCREW_NUT,M3X10-10,Nut"
                string[] parts = mountPointStr.Split(',');
                if (parts.Length >= 6)
                {
                    var mountPoint = new JSONObject();
                    mountPoint["partName"] = parts[0];
                    mountPoint["mountPoint"] = parts[1];
                    mountPoint["screwType"] = parts[4];
                    mountPoint["nutType"] = parts[5];
                    additionalMountPoints.Add(mountPoint);
                }
            }
        }
        stepObj["additionalMountPoints"] = additionalMountPoints;
        
        stepsArray.Add(stepObj);
        
        var result = new JSONObject();
        result["steps"] = stepsArray;
        
        return result.ToString();
    }
}