using UnityEngine;
using NativeWebSocket;
using System.Text;
using System.Collections;
using System;
using System.Threading.Tasks;
// using Unity.XR.PXR; // 1. 已移除PXR命名空间
using Woz.Logging; // 引入日志系统命名空间

// --- 数据结构定义 ---

// 从服务器接收的指令
[System.Serializable]
public class AnimationCommand
{
    public string command; // e.g., "play", "stop", "seethrough", "execute_step", "load_scene", "show_failure_feedback", "sequence_completed"
    public string animation_name;
    public float speed = 1.0f;
    public string state; // 用于透视命令: "on" 或 "off"

    // --- 序列模式新增字段 ---
    public string scene_id; // 用于 load_scene 命令
    public string message; // 用于 show_failure_feedback 命令
    public SequenceStepData step_data; // 用于 execute_step 命令
}

// 序列步骤数据
[System.Serializable]
public class SequenceStepData
{
    public string seq_id; // 新增：与JSON数据对齐
    public string movingPartName;
    public string targetPartName;
    public string movingPartRefPoint;
    public string targetPartRefPoint;
    public string connectionType;
    public string fastenerType;
    public string screwType;
    public string nutType;
    public System.Collections.Generic.List<string> additionalMountPoints; // 新增：与JSON数据对齐
    public int stepIndex;
    public int totalSteps;
    public string status; // "in_progress", "completed"
}

// 新增：通用的服务器消息基类，用于预解析消息类型
[System.Serializable]
public class ServerMessage
{
    public string type;
    public string client_id; // 新增：用于接收服务器分配的ID
}


// 发送给服务器的用户状态
[System.Serializable]
public class UserStatus
{
    public string status; // e.g., "playing_animation", "idle"
    public string current_animation;
    public HandPose left_hand_pose;
    public HandPose right_hand_pose;
}

[System.Serializable]
public class HandPose
{
    public Vector3 position;
    public Quaternion rotation;
    // 如果您需要，可以提供更多关于手指关节的详细信息
}

// 新增：用于向服务器发送身份识别信息
[System.Serializable]
public class ClientIdentification
{
    public string type = "client_identification";
    public string device_name;

    public ClientIdentification()
    {
        device_name = SystemInfo.deviceName;
    }
}


public class WebSocketManager : MonoBehaviour
{
    // --- Inspector配置 ---
    [Header("WebSocket Server Settings")]
    public string serverUrl = "ws://10.15.22.27:8000/vr";

    [Header("重连设置")]
    [SerializeField] private bool enableAutoReconnect = true; // 是否启用自动重连
    [SerializeField] private float fastReconnectIntervalSeconds = 0.5f; // 快速重连尝试的间隔
    [SerializeField] private float reconnectLogIntervalSeconds = 5f; // 日志记录的间隔
    [SerializeField] private int maxReconnectAttempts = -1; // 最大重连次数，-1表示无限重连
    [SerializeField] private bool showReconnectLogs = true; // 是否显示重连日志

    // --- 私有变量 ---
    private WebSocket websocket;
    // private string currentSeeThroughStatus = "unknown"; // 3. 移除了 currentSeeThroughStatus 状态变量
    private string clientId; // 新增：存储从服务器获取的客户端ID
    private bool isConnecting = false;
    private bool shouldAttemptReconnect = true;
    private int reconnectAttempts = 0;
    private bool connectionEstablished = false; // 用于准确判断连接是否成功建立
    private bool identificationRequested = false; // 新增：用于跟踪服务器是否已请求身份验证
    private float lastReconnectLogTimestamp = -10f; // 用于控制日志频率

    // 日志源标识
    private const string LOG_SOURCE = "WebSocketManager";

    // --- Unity生命周期函数 ---
    void Start()
    {
        // PXR_Manager.VstDisplayStatusChanged += VstDisplayStatusChanged; // 4. 移除了透视状态变化的事件监听

        LogManager.Info(LOG_SOURCE, "WebSocket管理器启动");

        // 启动连接管理
        StartCoroutine(ConnectionManager());
    }

    void Update()
    {
        if (IsConnected())
        {
#if !UNITY_WEBGL || UNITY_EDITOR
            // 在非WebGL平台，需要手动Dispatch消息
            websocket.DispatchMessageQueue();
#endif
        }
    }

    async void OnDestroy()
    {
        LogManager.Info(LOG_SOURCE, "WebSocket管理器正在关闭");

        shouldAttemptReconnect = false;

        // PXR_Manager.VstDisplayStatusChanged -= VstDisplayStatusChanged; // 5. 移除了事件监听的取消

        await CleanupWebSocketAsync();
    }

    // --- 连接管理 ---

    /// <summary>
    /// 连接管理器 - 负责连接、重连和仿真模式切换
    /// </summary>
    private IEnumerator ConnectionManager()
    {
        LogManager.Info(LOG_SOURCE, "启动连接管理器");

        while (shouldAttemptReconnect)
        {
            if (!isConnecting && !IsConnected())
            {
                // 等待间隔现在由AttemptConnectionCoroutine在失败时处理
                yield return StartCoroutine(AttemptConnectionCoroutine());
            }
            else
            {
                // 如果已连接，则每秒检查一次状态
                yield return new WaitForSeconds(1f);
            }
        }

        LogManager.Info(LOG_SOURCE, "连接管理器已停止");
    }

    /// <summary>
    /// 尝试连接到服务器，并实现快速重连与低频日志
    /// </summary>
    private IEnumerator AttemptConnectionCoroutine()
    {
        if (isConnecting) yield break;

        isConnecting = true;
        reconnectAttempts++;
        // 重置所有状态标志
        connectionEstablished = false;
        identificationRequested = false;

        // --- 低频日志逻辑 ---
        bool logThisAttempt = Time.time - lastReconnectLogTimestamp >= reconnectLogIntervalSeconds;
        if (logThisAttempt && showReconnectLogs)
        {
            lastReconnectLogTimestamp = Time.time;
            LogManager.Info(LOG_SOURCE, $"重连尝试 #{reconnectAttempts}...");
        }

        // 运行异步连接任务并等待其完成
        var connectTask = ConnectToServerAsync();
        yield return new WaitUntil(() => connectTask.IsCompleted);

        // --- 结果判断 ---
        // 关键修复：不再需要复杂的等待循环，直接在OnOpen和OnMessage中设置最终状态
        // 只需要在这里给一个短暂的延迟，以确保事件有足够的时间被处理
        yield return new WaitForSeconds(0.2f);

        isConnecting = false;

        if (connectionEstablished) // 现在只检查最终的成功标志
        {
            LogManager.Info(LOG_SOURCE, "连接成功并完成身份验证！");
            reconnectAttempts = 0;
        }
        else
        {
            // 如果任务失败，记录日志（可选，因为OnError已经记录了）
            if (connectTask.IsFaulted && logThisAttempt)
            {
                LogManager.Warning(LOG_SOURCE, $"底层连接任务失败: {connectTask.Exception.GetBaseException().Message}");
            }

            if (ShouldContinueReconnecting())
            {
                if (logThisAttempt && showReconnectLogs)
                {
                    LogManager.Info(LOG_SOURCE, $"下一次日志将在约 {reconnectLogIntervalSeconds}秒后显示 (后台快速重连中...)");
                }
                yield return new WaitForSeconds(fastReconnectIntervalSeconds);
            }
        }
    }

    // --- Async Operations (async/await) ---
    private async Task ConnectToServerAsync()
    {
        await CleanupWebSocketAsync();

        websocket = new WebSocket(serverUrl);

        websocket.OnOpen += WebSocket_OnOpen;
        websocket.OnError += WebSocket_OnError;
        websocket.OnClose += WebSocket_OnClose;
        websocket.OnMessage += WebSocket_OnMessage;

        await websocket.Connect();
    }

    private async Task CleanupWebSocketAsync()
    {
        if (websocket != null)
        {
            websocket.OnOpen -= WebSocket_OnOpen;
            websocket.OnError -= WebSocket_OnError;
            websocket.OnClose -= WebSocket_OnClose;
            websocket.OnMessage -= WebSocket_OnMessage;

            if (websocket.State == WebSocketState.Open || websocket.State == WebSocketState.Connecting)
            {
                await websocket.Close();
            }
            websocket = null;
        }
    }

    // --- WebSocket 事件处理器 ---
    private void WebSocket_OnOpen()
    {
        connectionEstablished = true; // 关键：现在这是判断成功的唯一标志
        LogManager.Info(LOG_SOURCE, "WebSocket连接已建立！");
        // OnOpen时不再自动发送身份或状态更新，等待服务器指令
    }
    private void WebSocket_OnError(string errorMsg)
    {
        if (showReconnectLogs) LogManager.Warning(LOG_SOURCE, $"WebSocket错误：{errorMsg}");
        // 关键修复：发生错误时，重置连接状态以触发重连
        connectionEstablished = false;
        isConnecting = false;
    }

    private void WebSocket_OnClose(WebSocketCloseCode closeCode)
    {
        if (showReconnectLogs) LogManager.Info(LOG_SOURCE, $"WebSocket连接已关闭，代码：{closeCode}");
        // 关键修复：连接关闭时，重置连接状态以触发重连
        connectionEstablished = false;
        isConnecting = false;
    }
    private void WebSocket_OnMessage(byte[] data)
    {
        var message = Encoding.UTF8.GetString(data);
        LogManager.Info(LOG_SOURCE, "收到服务器消息：" + message);
        ProcessServerCommand(message);
    }

    // --- 连接失败处理 ---
    private void HandleConnectionFailure()
    {
        // 此方法目前可以留空，因为所有重连逻辑都已移至AttemptConnectionCoroutine
    }

    // --- 辅助方法 ---
    private bool ShouldContinueReconnecting()
    {
        return enableAutoReconnect &&
               shouldAttemptReconnect &&
               (maxReconnectAttempts == -1 || reconnectAttempts < maxReconnectAttempts);
    }
    private bool IsConnected() { return websocket != null && websocket.State == WebSocketState.Open; }

    // --- 公共API ---
    public void SendStatusUpdate()
    {
        if (!IsConnected())
        {
            // 移除断开连接时的重复警告日志
            return;
        }

        UserStatus status = new UserStatus
        {
            status = "idle",
            current_animation = "none",
            left_hand_pose = new HandPose { position = Vector3.zero, rotation = Quaternion.identity },
            right_hand_pose = new HandPose { position = Vector3.zero, rotation = Quaternion.identity }
            // seethrough_status = currentSeeThroughStatus // 6. 移除了状态更新中的透视信息
        };

        SendWebSocketMessage(JsonUtility.ToJson(status));
    }

    public void SendExperimentData(string experimentDataJson)
    {
        if (!IsConnected())
        {
            LogManager.Warning(LOG_SOURCE, "WebSocket未连接，无法发送实验数据");
            return;
        }

        SendWebSocketMessage(experimentDataJson);
        LogManager.Info(LOG_SOURCE, $"已发送实验数据：{experimentDataJson}");
    }

    public void ProcessServerCommandPublic(string jsonMessage)
    {
        LogManager.Info(LOG_SOURCE, $"[测试模式] 处理模拟服务器命令：{jsonMessage}");
        ProcessServerCommand(jsonMessage);
    }

    [ContextMenu("手动重连")]
    public void ManualReconnect()
    {
        LogManager.Info(LOG_SOURCE, "手动触发重连");
        reconnectAttempts = 0;

        StartCoroutine(AttemptConnectionCoroutine());
    }

    [ContextMenu("停止重连")]
    public void StopReconnecting()
    {
        LogManager.Info(LOG_SOURCE, "停止自动重连");
        shouldAttemptReconnect = false;
    }

    [ContextMenu("开始重连")]
    public void StartReconnecting()
    {
        LogManager.Info(LOG_SOURCE, "启动自动重连");
        shouldAttemptReconnect = true;
        reconnectAttempts = 0;
        StartCoroutine(ConnectionManager());
    }

    public string GetConnectionStatus()
    {
        if (websocket == null)
        {
            return "未初始化";
        }
        else
        {
            switch (websocket.State)
            {
                case WebSocketState.Connecting:
                    return "连接中";
                case WebSocketState.Open:
                    return "已连接";
                case WebSocketState.Closing:
                    return "断开中";
                case WebSocketState.Closed:
                    return "已断开";
                default:
                    return "未知状态";
            }
        }
    }

    // --- 消息处理 ---

    private async void SendWebSocketMessage(string message)
    {
        if (IsConnected())
        {
            await websocket.SendText(message);
        }
    }

    // private void VstDisplayStatusChanged(PxrVstStatus status) { ... } // 7. 整个透视状态处理方法被移除

    private void ProcessServerCommand(string jsonMessage)
    {
        try
        {
            // 步骤1：预解析消息，获取消息类型
            ServerMessage genericMessage = JsonUtility.FromJson<ServerMessage>(jsonMessage);

            // 步骤2：根据消息类型选择不同的处理方式
            switch (genericMessage.type)
            {
                case "request_identification":
                    LogManager.Info(LOG_SOURCE, $"收到服务器的身份识别请求。");
                    this.clientId = genericMessage.client_id; // 存储客户端ID
                    LogManager.Info(LOG_SOURCE, $"已分配客户端ID: {this.clientId}");
                    // 收到请求后，再发送身份信息
                    SendIdentification();
                    break;

                case "connection_established": // 兼容旧版或未来可能的欢迎消息
                    LogManager.Info(LOG_SOURCE, $"与服务器连接已建立: {jsonMessage}");
                    break;

                case "assembly_command":
                    // 只有当确定是装配指令时，才完整解析为AnimationCommand
                    AnimationCommand command = JsonUtility.FromJson<AnimationCommand>(jsonMessage);
                    ProcessAssemblyCommand(command);
                    break;

                default:
                    LogManager.Warning(LOG_SOURCE, $"收到未知的服务器消息类型: '{genericMessage.type}'");
                    break;
            }
        }
        catch (System.Exception e)
        {
            LogManager.Error(LOG_SOURCE, $"解析服务器命令失败：{jsonMessage}。", e);
        }
    }

    private void ProcessAssemblyCommand(AnimationCommand command)
    {
        if (command == null || string.IsNullOrEmpty(command.command))
        {
            LogManager.Warning(LOG_SOURCE, "收到的装配指令无效或为空。");
            return;
        }

        switch (command.command.ToLower())
        {
            case "play":
                LogManager.Info(LOG_SOURCE, $"收到播放动画命令：{command.command}，动画名称：{command.animation_name}");
                // TODO: 在这里调用您的动画控制器来执行指令
                break;

            case "stop":
                LogManager.Info(LOG_SOURCE, $"收到停止动画命令：{command.command}");
                // TODO: 停止动画播放
                break;

            case "load_scene":
                var sb2 = new System.Text.StringBuilder();
                sb2.AppendLine("收到并开始处理'加载场景'指令");
                sb2.AppendLine("--------------------------------------------------");
                sb2.AppendLine($"  场景ID: {command.scene_id}");
                sb2.AppendLine("--------------------------------------------------");
                LogManager.Info(LOG_SOURCE, sb2.ToString());

                // 触发加载场景事件，而不是直接执行
                if (!string.IsNullOrEmpty(command.scene_id))
                {
                    EventManager.TriggerLoadScene(command.scene_id);
                }
                else
                {
                    LogManager.Warning(LOG_SOURCE, "收到的场景ID为空，无法加载。");
                }
                break;

            case "execute_step":
                if (command.step_data == null)
                {
                    LogManager.Error(LOG_SOURCE, "收到'execute_step'指令，但'step_data'为空！");
                    return;
                }

                // 增强指令日志，使其更清晰
                var sb = new System.Text.StringBuilder();
                sb.AppendLine("收到并开始处理'执行下一步'指令");
                sb.AppendLine("--------------------------------------------------");
                sb.AppendLine($"  步骤: {command.step_data.stepIndex + 1} / {command.step_data.totalSteps}");
                sb.AppendLine($"  步骤ID (seq_id): {command.step_data.seq_id}");
                sb.AppendLine($"  移动零件: {command.step_data.movingPartName}");
                sb.AppendLine($"  目标零件: {command.step_data.targetPartName}");
                sb.AppendLine($"  连接类型: {command.step_data.connectionType}");
                sb.AppendLine("--------------------------------------------------");
                LogManager.Info(LOG_SOURCE, sb.ToString());

                // 触发执行步骤事件
                EventManager.TriggerAssemblyStep(command.step_data);
                break;

            case "show_failure_feedback":
                LogManager.Info(LOG_SOURCE, $"收到失败反馈命令：{command.message}");
                // 触发失败反馈事件
                EventManager.TriggerFailureFeedback(command.message);
                break;

            case "sequence_completed":
                LogManager.Info(LOG_SOURCE, "收到装配序列完成命令");
                // 触发序列完成事件
                EventManager.TriggerSequenceCompleted();
                break;
            
            // 7. 新增透视指令处理
            case "seethrough":
                LogManager.Info(LOG_SOURCE, $"收到透视切换命令：状态 = {command.state}");
                // 触发透视切换事件
                EventManager.TriggerSeeThrough(command.state);
                break;

            default:
                LogManager.Warning(LOG_SOURCE, $"未知装配指令：{command.command}");
                break;
        }
    }

    // --- 新增方法 ---
    private void SendIdentification()
    {
        if (!IsConnected()) return;

        var identificationMsg = new ClientIdentification();
        string jsonMessage = JsonUtility.ToJson(identificationMsg);

        LogManager.Info(LOG_SOURCE, $"正在发送身份识别信息: {jsonMessage}");
        SendWebSocketMessage(jsonMessage);
    }
}