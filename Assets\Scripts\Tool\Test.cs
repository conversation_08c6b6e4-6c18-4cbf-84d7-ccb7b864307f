using UnityEngine;

public class Test : MonoBehaviour
{
    public Transform mountPoint; // 安装点的 Transform
    public Vector3 mountPointAxis = Vector3.forward; // 安装点的局部轴（默认 Z 轴）
    public KeyCode alignKey = KeyCode.Space; // 触发对齐的按键
    public float rotationSpeed = 5f; // 旋转速度（越大越快）

    private Quaternion targetRotation; // 目标旋转
    private bool isRotating; // 是否正在旋转

    void Start()
    {
        // 初始化目标旋转为当前旋转
        targetRotation = transform.rotation;
    }

    void Update()
    {
        // 检查必要组件
        if (mountPoint == null || Camera.main == null)
        {
            Debug.LogWarning("MountPoint or Main Camera is not assigned!");
            return;
        }

        // 检测按键
        if (Input.GetKeyDown(alignKey))
        {
            // 计算目标方向（从 MountPoint 到摄像机的方向）
            Vector3 cameraPos = Camera.main.transform.position;
            Vector3 mountPointPos = mountPoint.position;
            Vector3 targetDirection = (cameraPos - mountPointPos).normalized;

            // 获取 MountPoint 当前的世界坐标系中的指定轴方向
            Vector3 currentWorldAxis = mountPoint.TransformDirection(mountPointAxis.normalized);

            // 计算从当前轴方向到目标方向的旋转
            Quaternion rotationToTarget = Quaternion.FromToRotation(currentWorldAxis, targetDirection);

            // 获取 Parent 当前的世界旋转
            Quaternion parentWorldRotation = transform.rotation;

            // 计算 Parent 的目标世界旋转
            targetRotation = rotationToTarget * parentWorldRotation;

            // 开始旋转
            isRotating = true;
        }

        // 平滑旋转到目标
        if (isRotating)
        {
            // 使用 Slerp 平滑插值
            transform.rotation = Quaternion.Slerp(transform.rotation, targetRotation, Time.deltaTime * rotationSpeed);

            // 检查是否接近目标旋转
            if (Quaternion.Angle(transform.rotation, targetRotation) < 0.1f)
            {
                transform.rotation = targetRotation; // 确保精确到达
                isRotating = false; // 停止旋转
            }
        }
    }

    // 可视化调试（可选）
    void OnDrawGizmos()
    {
        if (mountPoint != null)
        {
            // 显示 MountPoint 的当前轴
            Vector3 currentAxis = mountPoint.TransformDirection(mountPointAxis.normalized);
            Gizmos.color = Color.blue;
            Gizmos.DrawRay(mountPoint.position, currentAxis * 2f);

            // 显示目标方向（摄像机方向）
            if (Camera.main != null)
            {
                Vector3 targetDir = (Camera.main.transform.position - mountPoint.position).normalized;
                Gizmos.color = Color.green;
                Gizmos.DrawRay(mountPoint.position, targetDir * 2f);
            }
        }
    }
}