using UnityEngine;
using System.Collections;
using System.Collections.Generic;

/// <summary>
/// VR装配预览系统
/// 
/// 在用户手边创建小型3D预览模型，自动旋转展示，
/// 让用户可以从各个角度观察装配过程
/// </summary>
public class VRAssemblyPreview : MonoBehaviour
{
    [Header("预览设置")]
    [SerializeField] private float previewScale = 0.3f;
    [SerializeField] private Vector3 previewOffset = new Vector3(0.5f, 0.2f, 0.8f);
    [SerializeField] private float rotationSpeed = 30f;
    [SerializeField] private bool autoRotate = true;
    
    [Header("预览位置")]
    [SerializeField] private Transform previewAnchor;
    [SerializeField] private bool useRightHandPosition = true;
    [SerializeField] private bool useLeftHandPosition = false;
    
    [Header("预览材质")]
    [SerializeField] private Material previewMaterial;
    [SerializeField] private Material highlightMaterial;
    [SerializeField] private bool useTransparentPreview = true;
    [SerializeField] private float previewAlpha = 0.7f;
    
    [Header("UI设置")]
    [SerializeField] private Canvas previewCanvas;
    [SerializeField] private UnityEngine.UI.Text previewLabel;
    
    // 内部状态
    private GameObject currentPreview;
    private List<GameObject> previewParts = new List<GameObject>();
    private Coroutine rotationCoroutine;
    private bool isPreviewActive = false;

    void Start()
    {
        InitializePreview();
    }

    /// <summary>
    /// 初始化预览系统
    /// </summary>
    private void InitializePreview()
    {
        // 如果没有指定预览锚点，尝试查找VR控制器
        if (previewAnchor == null)
        {
            previewAnchor = FindVRControllerTransform();
        }

        // 如果仍然没有找到，使用摄像机作为参考
        if (previewAnchor == null)
        {
            previewAnchor = Camera.main?.transform;
            Debug.LogWarning("[VRAssemblyPreview] 未找到VR控制器，使用摄像机作为预览锚点");
        }

        // 创建预览材质
        if (previewMaterial == null && useTransparentPreview)
        {
            CreateTransparentPreviewMaterial();
        }
    }

    /// <summary>
    /// 查找VR控制器Transform
    /// </summary>
    private Transform FindVRControllerTransform()
    {
        // 这里可以根据具体的VR SDK来查找控制器
        // 暂时返回null，后续可以扩展
        return null;
    }

    /// <summary>
    /// 创建透明预览材质
    /// </summary>
    private void CreateTransparentPreviewMaterial()
    {
        previewMaterial = new Material(Shader.Find("Standard"));
        previewMaterial.SetFloat("_Mode", 3); // Transparent mode
        previewMaterial.SetInt("_SrcBlend", (int)UnityEngine.Rendering.BlendMode.SrcAlpha);
        previewMaterial.SetInt("_DstBlend", (int)UnityEngine.Rendering.BlendMode.OneMinusSrcAlpha);
        previewMaterial.SetInt("_ZWrite", 0);
        previewMaterial.DisableKeyword("_ALPHATEST_ON");
        previewMaterial.EnableKeyword("_ALPHABLEND_ON");
        previewMaterial.DisableKeyword("_ALPHAPREMULTIPLY_ON");
        previewMaterial.renderQueue = 3000;
        
        Color color = previewMaterial.color;
        color.a = previewAlpha;
        previewMaterial.color = color;
    }

    /// <summary>
    /// 创建装配预览
    /// </summary>
    public GameObject CreateAssemblyPreview(AssemblyPart[] parts, string stepDescription = "")
    {
        if (parts == null || parts.Length == 0)
        {
            Debug.LogWarning("[VRAssemblyPreview] 没有提供装配零件");
            return null;
        }

        // 清理之前的预览
        ClearPreview();

        // 计算预览位置
        Vector3 previewPosition = CalculatePreviewPosition();

        // 创建预览容器
        currentPreview = new GameObject("AssemblyPreview");
        currentPreview.transform.position = previewPosition;
        currentPreview.transform.localScale = Vector3.one * previewScale;

        // 复制装配零件到预览中
        foreach (var part in parts)
        {
            if (part != null)
            {
                GameObject previewPart = CreatePreviewPart(part.gameObject, currentPreview.transform);
                previewParts.Add(previewPart);
            }
        }

        // 更新预览标签
        UpdatePreviewLabel(stepDescription);

        // 开始自动旋转
        if (autoRotate)
        {
            StartAutoRotation();
        }

        isPreviewActive = true;

        Debug.Log($"[VRAssemblyPreview] 创建预览，包含 {previewParts.Count} 个零件");
        return currentPreview;
    }

    /// <summary>
    /// 创建单个零件的预览
    /// </summary>
    private GameObject CreatePreviewPart(GameObject originalPart, Transform parent)
    {
        // 复制原始零件
        GameObject previewPart = Instantiate(originalPart, parent);
        previewPart.name = originalPart.name + "_Preview";

        // 移除不需要的组件
        RemoveUnnecessaryComponents(previewPart);

        // 应用预览材质
        ApplyPreviewMaterial(previewPart);

        // 保持原始的相对位置和旋转
        previewPart.transform.localPosition = originalPart.transform.localPosition;
        previewPart.transform.localRotation = originalPart.transform.localRotation;
        previewPart.transform.localScale = originalPart.transform.localScale;

        return previewPart;
    }

    /// <summary>
    /// 移除预览中不需要的组件
    /// </summary>
    private void RemoveUnnecessaryComponents(GameObject previewPart)
    {
        // 移除物理组件
        Rigidbody rb = previewPart.GetComponent<Rigidbody>();
        if (rb != null) DestroyImmediate(rb);

        Collider[] colliders = previewPart.GetComponentsInChildren<Collider>();
        foreach (var collider in colliders)
        {
            DestroyImmediate(collider);
        }

        // 移除AssemblyPart组件
        AssemblyPart assemblyPart = previewPart.GetComponent<AssemblyPart>();
        if (assemblyPart != null) DestroyImmediate(assemblyPart);

        // 移除其他脚本组件
        MonoBehaviour[] scripts = previewPart.GetComponentsInChildren<MonoBehaviour>();
        foreach (var script in scripts)
        {
            if (script != null)
            {
                DestroyImmediate(script);
            }
        }
    }

    /// <summary>
    /// 应用预览材质
    /// </summary>
    private void ApplyPreviewMaterial(GameObject previewPart)
    {
        Renderer[] renderers = previewPart.GetComponentsInChildren<Renderer>();
        foreach (var renderer in renderers)
        {
            if (previewMaterial != null)
            {
                Material[] materials = new Material[renderer.materials.Length];
                for (int i = 0; i < materials.Length; i++)
                {
                    materials[i] = previewMaterial;
                }
                renderer.materials = materials;
            }
        }
    }

    /// <summary>
    /// 计算预览位置
    /// </summary>
    private Vector3 CalculatePreviewPosition()
    {
        if (previewAnchor == null)
        {
            Debug.LogWarning("[VRAssemblyPreview] 预览锚点未设置，使用默认位置");
            return Vector3.zero;
        }

        // 根据锚点计算预览位置
        Vector3 anchorPosition = previewAnchor.position;
        Vector3 anchorForward = previewAnchor.forward;
        Vector3 anchorRight = previewAnchor.right;
        Vector3 anchorUp = previewAnchor.up;

        // 应用偏移
        Vector3 previewPosition = anchorPosition +
                                 anchorRight * previewOffset.x +
                                 anchorUp * previewOffset.y +
                                 anchorForward * previewOffset.z;

        return previewPosition;
    }

    /// <summary>
    /// 开始自动旋转
    /// </summary>
    private void StartAutoRotation()
    {
        if (rotationCoroutine != null)
        {
            StopCoroutine(rotationCoroutine);
        }
        rotationCoroutine = StartCoroutine(AutoRotatePreview());
    }

    /// <summary>
    /// 自动旋转预览协程
    /// </summary>
    private IEnumerator AutoRotatePreview()
    {
        while (currentPreview != null && isPreviewActive)
        {
            currentPreview.transform.Rotate(0, rotationSpeed * Time.deltaTime, 0);
            yield return null;
        }
    }

    /// <summary>
    /// 停止自动旋转
    /// </summary>
    public void StopAutoRotation()
    {
        if (rotationCoroutine != null)
        {
            StopCoroutine(rotationCoroutine);
            rotationCoroutine = null;
        }
    }

    /// <summary>
    /// 高亮显示特定零件
    /// </summary>
    public void HighlightPart(string partName)
    {
        foreach (var previewPart in previewParts)
        {
            if (previewPart.name.Contains(partName))
            {
                ApplyHighlightMaterial(previewPart);
            }
        }
    }

    /// <summary>
    /// 应用高亮材质
    /// </summary>
    private void ApplyHighlightMaterial(GameObject part)
    {
        if (highlightMaterial == null) return;

        Renderer[] renderers = part.GetComponentsInChildren<Renderer>();
        foreach (var renderer in renderers)
        {
            Material[] materials = new Material[renderer.materials.Length];
            for (int i = 0; i < materials.Length; i++)
            {
                materials[i] = highlightMaterial;
            }
            renderer.materials = materials;
        }
    }

    /// <summary>
    /// 更新预览标签
    /// </summary>
    private void UpdatePreviewLabel(string text)
    {
        if (previewLabel != null)
        {
            previewLabel.text = text;
        }
    }

    /// <summary>
    /// 清理预览
    /// </summary>
    public void ClearPreview()
    {
        StopAutoRotation();

        if (currentPreview != null)
        {
            DestroyImmediate(currentPreview);
            currentPreview = null;
        }

        previewParts.Clear();
        isPreviewActive = false;

        Debug.Log("[VRAssemblyPreview] 预览已清理");
    }

    /// <summary>
    /// 设置预览可见性
    /// </summary>
    public void SetPreviewVisible(bool visible)
    {
        if (currentPreview != null)
        {
            currentPreview.SetActive(visible);
        }
    }

    /// <summary>
    /// 更新预览位置（跟随手部移动）
    /// </summary>
    void Update()
    {
        if (isPreviewActive && currentPreview != null && previewAnchor != null)
        {
            // 平滑跟随锚点位置
            Vector3 targetPosition = CalculatePreviewPosition();
            currentPreview.transform.position = Vector3.Lerp(
                currentPreview.transform.position, 
                targetPosition, 
                Time.deltaTime * 2f
            );
        }
    }

    /// <summary>
    /// 公共方法：为装配步骤创建预览
    /// </summary>
    public void CreateStepPreview(AssemblyPart movingPart, AssemblyPart targetPart, string stepDescription)
    {
        AssemblyPart[] parts = { movingPart, targetPart };
        CreateAssemblyPreview(parts, stepDescription);
    }

    /// <summary>
    /// 公共方法：装配步骤结束时清理预览
    /// </summary>
    public void OnAssemblyStepEnd()
    {
        ClearPreview();
    }

    void OnDestroy()
    {
        ClearPreview();
    }
}
