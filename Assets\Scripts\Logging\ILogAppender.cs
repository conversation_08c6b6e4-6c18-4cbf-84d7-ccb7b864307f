using System;

namespace Woz.Logging
{
    /// <summary>
    /// 日志等级
    /// </summary>
    public enum LogLevel
    {
        DEBUG = 0,
        INFO = 1,
        WARNING = 2,
        ERROR = 3,
        FATAL = 4
    }

    /// <summary>
    /// 日志条目结构体，用于在系统中传递日志数据
    /// </summary>
    public struct LogEntry
    {
        public DateTime Timestamp { get; }
        public LogLevel Level { get; }
        public string Source { get; } // 日志来源 (类名/模块名)
        public string Message { get; }

        public LogEntry(LogLevel level, string source, string message)
        {
            Timestamp = DateTime.Now;
            Level = level;
            Source = source;
            Message = message;
        }
    }

    /// <summary>
    /// 日志输出器接口，所有输出方式（控制台、文件等）都必须实现此接口
    /// </summary>
    public interface ILogAppender : IDisposable
    {
        /// <summary>
        /// 记录一条日志
        /// </summary>
        /// <param name="entry">日志条目</param>
        void Log(LogEntry entry);
    }
} 