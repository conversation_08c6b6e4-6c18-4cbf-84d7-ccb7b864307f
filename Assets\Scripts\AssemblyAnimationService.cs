using System.Collections;
using System.Collections.Generic;
using UnityEngine;

/**
 * 装配动画服务类
 *
 * 提供通用的装配动画功能，包括零件对齐、螺丝和螺母安装
 * 可被其他控制器调用，不再依赖特定的U型架配置
 */
public class AssemblyAnimationService : MonoBehaviour
{
    [Header("动画设置")]
    [SerializeField] private float animationDuration = 1.0f; // 动画持续时间

    [Header("螺丝安装设置")]
    [SerializeField] private float screwLength = 0.05f; // 螺丝长度
    [SerializeField] private float nutThickness = 0.01f; // 螺母厚度
    [SerializeField] private float initialDistanceFromHole = 0.1f; // 螺丝初始距离孔位的距离
    [SerializeField] private float screwInitialElevation = 0.05f; // 螺丝初始高度

    private AssemblyAnimationManager animationManager;

    /// <summary>
    /// 获取动画管理器实例
    /// </summary>
    public AssemblyAnimationManager AnimationManager => animationManager;

    void Awake()
    {
        // 初始化动画管理器
        animationManager = new AssemblyAnimationManager();
    }

    void Start()
    {
        // 装配动画服务不再自动启动动画，而是等待外部调用
        Debug.Log("装配动画服务已初始化，等待外部调用");
    }



    /**
     * 公共方法：对齐任意两个零件
     */
    public void AlignPartsPublic(
        AssemblyPart movingPart,
        AssemblyPart targetPart,
        int movingPartRefIndex = 0,
        int targetPartRefIndex = 0,
        float duration = 1.0f
    )
    {
        StartCoroutine(animationManager.AlignParts(movingPart, targetPart, movingPartRefIndex, targetPartRefIndex, duration));
    }

    /**
     * 公共方法：移动螺丝到孔位并安装
     */
    public IEnumerator MoveScrewImprovedPublic(
        AssemblyPart screwPart,
        Transform holeRef,
        Vector3 holeDirection,
        int index = 0
    )
    {
        return MoveScrewImproved(screwPart, holeRef, holeDirection, index);
    }

    /**
     * 公共方法：移动螺母到螺丝末端并安装
     */
    public IEnumerator MoveNutImprovedPublic(
        AssemblyPart nutPart,
        Transform holeRef,
        Vector3 holeDirection,
        int index = 0
    )
    {
        return MoveNutImproved(nutPart, holeRef, holeDirection, index);
    }





    /**
     * 获取指定轴的方向向量
     */
    private Vector3 GetAxisDirection(Transform axisTransform)
    {
        if (axisTransform == null)
            return Vector3.forward;

        // 使用前方向作为轴向
        return axisTransform.forward;
    }

    /**
     * 改进的螺丝移动方法
     */
    IEnumerator MoveScrewImproved(AssemblyPart screwPart, Transform longHole, Vector3 holeDirection, int index)
    {
        if (screwPart == null || longHole == null)
        {
            Debug.LogError($"螺丝或孔位无效: 索引 {index}");
            yield break;
        }

        // 计算螺丝的起始位置和目标位置
        // 注意：螺丝应该从孔位的反方向插入，所以使用 -holeDirection
        Vector3 screwStartPos = longHole.position - holeDirection * initialDistanceFromHole + Vector3.up * screwInitialElevation;
        Vector3 screwEndPos = longHole.position + holeDirection * (screwLength * 0.5f);

        // 1. 移动螺丝到起始位置 - 注意旋转方向是 -holeDirection（螺丝头朝向孔位）
        yield return animationManager.MovePart(screwPart, screwStartPos, Quaternion.LookRotation(-holeDirection), animationDuration * 0.3f, true);

        // 2. 移动螺丝穿过孔位
        yield return animationManager.MovePart(screwPart, screwEndPos, null, animationDuration * 0.3f, true);

        // 3. 旋转螺丝（模拟拧紧）- 使用RotatePartInPlace方法保持挂载点位置不变
        // 计算目标旋转 - 在当前旋转基础上绕holeDirection轴旋转720度
        Quaternion currentRot = screwPart.PartTransform.rotation;
        Quaternion targetRot = currentRot * Quaternion.AngleAxis(720f, holeDirection);
        yield return animationManager.RotatePartInPlace(screwPart, targetRot, animationDuration * 0.4f);
    }

    /**
     * 改进的螺母移动方法
     */
    IEnumerator MoveNutImproved(AssemblyPart nutPart, Transform longHole, Vector3 holeDirection, int index)
    {
        if (nutPart == null || longHole == null)
        {
            Debug.LogError($"螺母或孔位无效: 索引 {index}");
            yield break;
        }

        // 计算螺丝末端位置（螺母的目标位置）
        Vector3 screwEndPosition = longHole.position + holeDirection * screwLength;

        // 计算螺母的起始位置（在螺丝末端上方）
        Vector3 nutStartPos = screwEndPosition + Vector3.up * 0.03f + holeDirection * (initialDistanceFromHole * 0.3f);

        // 计算螺母的最终位置（拧紧后的位置）
        Vector3 nutEndPos = screwEndPosition - holeDirection * (nutThickness * 0.7f);

        // 1. 移动螺母到起始位置 - 注意旋转方向是 -holeDirection（螺母内孔朝向螺丝）
        yield return animationManager.MovePart(nutPart, nutStartPos, Quaternion.LookRotation(-holeDirection), animationDuration * 0.3f, true);

        // 2. 移动螺母到螺丝末端
        yield return animationManager.MovePart(nutPart, screwEndPosition, null, animationDuration * 0.3f, true);

        // 3. 旋转螺母（模拟拧紧）- 使用RotatePartInPlace方法保持挂载点位置不变
        Quaternion currentNutRot = nutPart.PartTransform.rotation;
        Quaternion targetNutRot = currentNutRot * Quaternion.AngleAxis(720f, holeDirection);
        yield return animationManager.RotatePartInPlace(nutPart, targetNutRot, animationDuration * 0.3f);

        // 4. 移动螺母到最终位置（拧紧后的位置）
        yield return animationManager.MovePart(nutPart, nutEndPos, null, animationDuration * 0.1f, true);
    }

}
