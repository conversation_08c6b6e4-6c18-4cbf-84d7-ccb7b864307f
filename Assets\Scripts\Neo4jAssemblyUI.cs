using UnityEngine;
using UnityEngine.UI;
using System.Collections;
using System.Collections.Generic;

/// <summary>
/// Neo4j装配用户界面
///
/// 提供用户界面控制装配过程
/// </summary>
public class Neo4jAssemblyUI : MonoBehaviour
{
    [Header("控制器引用")]
    [SerializeField] private Neo4jAssemblyController assemblyController;

    [Header("UI元素")]
    [SerializeField] private Text statusText; // 状态文本
    [SerializeField] private Text selectedPartText; // 选中零件文本
    [SerializeField] private Text stepsCountText; // 步骤计数文本
    [SerializeField] private Button nextStepButton; // 下一步按钮
    [SerializeField] private Button resetButton; // 重置按钮
    [SerializeField] private Toggle autoPlayToggle; // 自动播放开关
    [SerializeField] private Button replayButton; // 重播按钮
    [SerializeField] private Slider speedSlider; // 速率控制滑动条
    [SerializeField] private Text speedValueText; // 速率值文本

    [Header("UI设置")]
    [SerializeField] private float autoPlayDelay = 1.0f; // 自动播放延迟
    [SerializeField] private float minSpeedRate = 0.1f; // 最小速率
    [SerializeField] private float maxSpeedRate = 5.0f; // 最大速率

    // 内部状态
    private bool isAutoPlaying = false;
    private Coroutine autoPlayCoroutine;

    void Start()
    {
        // 验证必要组件
        if (assemblyController == null)
        {
            assemblyController = FindObjectOfType<Neo4jAssemblyController>();
            if (assemblyController == null)
            {
                Debug.LogError("Neo4jAssemblyController组件未找到！请手动指定。");
                enabled = false;
                return;
            }
        }

        // 初始化UI
        InitializeUI();
    }

    /// <summary>
    /// 初始化UI元素
    /// </summary>
    private void InitializeUI()
    {
        // 设置按钮事件
        if (nextStepButton != null)
        {
            nextStepButton.onClick.AddListener(OnNextStepButtonClicked);
        }

        if (resetButton != null)
        {
            resetButton.onClick.AddListener(OnResetButtonClicked);
        }

        if (autoPlayToggle != null)
        {
            autoPlayToggle.onValueChanged.AddListener(OnAutoPlayToggleChanged);
        }

        // 设置重播按钮事件
        if (replayButton != null)
        {
            replayButton.onClick.AddListener(OnReplayButtonClicked);
            replayButton.interactable = false; // 初始状态下禁用重播按钮
        }

        // 设置速率滑动条
        if (speedSlider != null)
        {
            // 设置滑动条范围
            speedSlider.minValue = minSpeedRate;
            speedSlider.maxValue = maxSpeedRate;
            speedSlider.value = 1.0f; // 默认速率为1.0

            // 添加事件监听
            speedSlider.onValueChanged.AddListener(OnSpeedSliderChanged);

            // 更新速率文本
            UpdateSpeedValueText(speedSlider.value);
        }

        // 初始化状态文本
        UpdateStatusText("请选择起始零件");

        // 初始状态下禁用下一步按钮
        if (nextStepButton != null)
        {
            nextStepButton.interactable = false;
        }
    }

    /// <summary>
    /// 更新状态文本
    /// </summary>
    /// <param name="message">状态消息</param>
    public void UpdateStatusText(string message)
    {
        if (statusText != null)
        {
            statusText.text = message;
        }
    }

    /// <summary>
    /// 更新选中零件文本
    /// </summary>
    /// <param name="partName">零件名称</param>
    public void UpdateSelectedPartText(string partName)
    {
        if (selectedPartText != null)
        {
            selectedPartText.text = string.IsNullOrEmpty(partName) ? "无" : partName;
        }
    }

    /// <summary>
    /// 更新步骤计数文本
    /// </summary>
    /// <param name="current">当前步骤</param>
    /// <param name="total">总步骤数</param>
    public void UpdateStepsCountText(int current, int total)
    {
        if (stepsCountText != null)
        {
            stepsCountText.text = $"{current}/{total}";
        }
    }

    /// <summary>
    /// 下一步按钮点击事件
    /// </summary>
    private void OnNextStepButtonClicked()
    {
        if (assemblyController != null)
        {
            assemblyController.ExecuteNextStep();
        }
    }

    /// <summary>
    /// 重置按钮点击事件
    /// </summary>
    private void OnResetButtonClicked()
    {
        // 停止自动播放
        StopAutoPlay();

        if (autoPlayToggle != null)
        {
            autoPlayToggle.isOn = false;
        }

        if (assemblyController != null)
        {
            assemblyController.ResetAssembly();
        }

        // 重置UI
        UpdateStatusText("请选择起始零件");
        UpdateSelectedPartText("");
        UpdateStepsCountText(0, 0);

        if (nextStepButton != null)
        {
            nextStepButton.interactable = false;
        }

        // 禁用重播按钮
        EnableReplayButton(false);
    }

    /// <summary>
    /// 自动播放开关变化事件
    /// </summary>
    /// <param name="isOn">是否开启</param>
    private void OnAutoPlayToggleChanged(bool isOn)
    {
        if (isOn)
        {
            StartAutoPlay();
        }
        else
        {
            StopAutoPlay();
        }
    }

    /// <summary>
    /// 开始自动播放
    /// </summary>
    private void StartAutoPlay()
    {
        if (!isAutoPlaying && assemblyController != null)
        {
            isAutoPlaying = true;
            autoPlayCoroutine = StartCoroutine(AutoPlayCoroutine());
        }
    }

    /// <summary>
    /// 停止自动播放
    /// </summary>
    private void StopAutoPlay()
    {
        if (isAutoPlaying)
        {
            isAutoPlaying = false;
            if (autoPlayCoroutine != null)
            {
                StopCoroutine(autoPlayCoroutine);
                autoPlayCoroutine = null;
            }
        }
    }

    /// <summary>
    /// 自动播放协程
    /// </summary>
    private IEnumerator AutoPlayCoroutine()
    {
        while (isAutoPlaying && assemblyController != null)
        {
            if (assemblyController.HasRemainingSteps())
            {
                assemblyController.ExecuteNextStep();
                yield return new WaitForSeconds(autoPlayDelay + assemblyController.AnimationDuration);
            }
            else
            {
                // 没有更多步骤，停止自动播放
                isAutoPlaying = false;
                if (autoPlayToggle != null)
                {
                    autoPlayToggle.isOn = false;
                }
                break;
            }
        }
    }

    /// <summary>
    /// 启用下一步按钮
    /// </summary>
    /// <param name="enable">是否启用</param>
    public void EnableNextStepButton(bool enable)
    {
        if (nextStepButton != null)
        {
            nextStepButton.interactable = enable;
        }
    }

    /// <summary>
    /// 启用重播按钮
    /// </summary>
    /// <param name="enable">是否启用</param>
    public void EnableReplayButton(bool enable)
    {
        if (replayButton != null)
        {
            replayButton.interactable = enable;
        }
    }

    /// <summary>
    /// 重播按钮点击事件
    /// </summary>
    private void OnReplayButtonClicked()
    {
        if (assemblyController != null)
        {
            assemblyController.ReplayLastStep();
        }
    }

    /// <summary>
    /// 速率滑动条变化事件
    /// </summary>
    /// <param name="value">新的速率值</param>
    private void OnSpeedSliderChanged(float value)
    {
        if (assemblyController != null)
        {
            // 更新控制器的动画速率
            assemblyController.AnimationSpeedRate = value;

            // 更新速率文本
            UpdateSpeedValueText(value);
        }
    }

    /// <summary>
    /// 更新速率值文本
    /// </summary>
    /// <param name="value">速率值</param>
    private void UpdateSpeedValueText(float value)
    {
        if (speedValueText != null)
        {
            speedValueText.text = $"速率: {value:F1}x";
        }
    }

    /// <summary>
    /// 零件选择事件处理
    /// </summary>
    /// <param name="partName">选中的零件名称</param>
    public void OnPartSelected(string partName)
    {
        UpdateSelectedPartText(partName);
        UpdateStatusText("正在查询装配关系...");
    }

    /// <summary>
    /// 装配步骤加载完成事件处理
    /// </summary>
    /// <param name="stepsCount">步骤总数</param>
    public void OnAssemblyStepsLoaded(int stepsCount)
    {
        UpdateStepsCountText(0, stepsCount);
        UpdateStatusText($"已加载 {stepsCount} 个装配步骤，准备开始");
        EnableNextStepButton(stepsCount > 0);
    }

    /// <summary>
    /// 装配步骤执行事件处理
    /// </summary>
    /// <param name="currentStep">当前步骤</param>
    /// <param name="totalSteps">总步骤数</param>
    /// <param name="stepDescription">步骤描述</param>
    public void OnStepExecuted(int currentStep, int totalSteps, string stepDescription)
    {
        UpdateStepsCountText(currentStep, totalSteps);
        UpdateStatusText(stepDescription);
        EnableNextStepButton(currentStep < totalSteps);

        // 启用重播按钮，因为现在有步骤可以重播
        EnableReplayButton(true);
    }

    /// <summary>
    /// 装配完成事件处理
    /// </summary>
    public void OnAssemblyCompleted()
    {
        UpdateStatusText("装配完成！");
        EnableNextStepButton(false);
        StopAutoPlay();

        if (autoPlayToggle != null)
        {
            autoPlayToggle.isOn = false;
        }

        // 保持重播按钮启用，以便用户可以重播最后一步
    }
}
