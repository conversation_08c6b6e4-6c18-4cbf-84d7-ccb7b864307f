using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using SimpleJSON;

/// <summary>
/// 装配数据提供者接口
/// 支持多种数据源：外部系统、本地文件、Neo4j等
/// </summary>
public interface IAssemblyDataProvider
{
    /// <summary>
    /// 加载装配步骤
    /// </summary>
    /// <param name="startPartName">起始零件名称</param>
    /// <param name="callback">回调函数，返回装配步骤列表</param>
    /// <returns>协程</returns>
    IEnumerator LoadAssemblySteps(string startPartName, Action<List<AssemblyStepData>> callback);

    /// <summary>
    /// 测试数据源连接
    /// </summary>
    /// <param name="callback">回调函数，返回连接状态</param>
    /// <returns>协程</returns>
    IEnumerator TestConnection(Action<bool> callback);

    /// <summary>
    /// 获取数据源类型
    /// </summary>
    string DataSourceType { get; }

    /// <summary>
    /// 获取数据源状态
    /// </summary>
    bool IsConnected { get; }
}

/// <summary>
/// 装配步骤数据结构
/// 标准化的数据格式，与具体数据源无关
/// </summary>
[System.Serializable]
public struct AssemblyStepData
{
    public string movingPartName;           // 移动零件名称
    public string targetPartName;           // 目标零件名称
    public string movingPartRefPoint;       // 移动零件参考点
    public string targetPartRefPoint;       // 目标零件参考点
    public string connectionType;           // 连接类型
    public FastenerData fastener;           // 紧固件信息
    public List<AdditionalMountPointData> additionalMountPoints; // 额外装配点

    /// <summary>
    /// 紧固件数据结构
    /// </summary>
    [System.Serializable]
    public struct FastenerData
    {
        public string type;            // 紧固件类型
        public string screwType;       // 螺丝类型
        public string nutType;         // 螺母类型
    }

    /// <summary>
    /// 额外装配点数据结构
    /// </summary>
    [System.Serializable]
    public struct AdditionalMountPointData
    {
        public string partName;        // 零件名称
        public string mountPoint;      // 装配点名称
        public string screwType;       // 螺丝类型
        public string nutType;         // 螺母类型
    }

    public override string ToString()
    {
        return $"{movingPartName}[{movingPartRefPoint}] -> {targetPartName}[{targetPartRefPoint}] ({connectionType})";
    }
}

/// <summary>
/// 外部系统数据提供者
/// 接收来自外部系统的装配数据
/// </summary>
public class ExternalSystemDataProvider : MonoBehaviour, IAssemblyDataProvider
{
    [Header("外部系统设置")]
    [SerializeField] private string apiEndpoint = "http://localhost:8080/api/assembly";
    [SerializeField] private float requestTimeout = 10f;
    [SerializeField] private bool useLocalFallback = true;

    [Header("本地备用数据")]
    [SerializeField] private TextAsset fallbackDataFile;

    public string DataSourceType => "External System";
    public bool IsConnected { get; private set; } = false;

    // 缓存的装配数据
    private Dictionary<string, List<AssemblyStepData>> cachedAssemblyData = new Dictionary<string, List<AssemblyStepData>>();

    void Start()
    {
        StartCoroutine(TestConnection((connected) => {
            IsConnected = connected;
            Debug.Log($"[ExternalSystemDataProvider] 连接状态: {(connected ? "已连接" : "未连接")}");
        }));
    }

    /// <summary>
    /// 从外部系统加载装配步骤
    /// </summary>
    public IEnumerator LoadAssemblySteps(string startPartName, Action<List<AssemblyStepData>> callback)
    {
        Debug.Log($"[ExternalSystemDataProvider] 请求装配数据: {startPartName}");

        // 首先检查缓存
        if (cachedAssemblyData.ContainsKey(startPartName))
        {
            Debug.Log($"[ExternalSystemDataProvider] 使用缓存数据: {startPartName}");
            callback?.Invoke(cachedAssemblyData[startPartName]);
            yield break;
        }

        // 尝试从外部系统获取数据
        yield return StartCoroutine(RequestAssemblyDataFromExternalSystem(startPartName, (success, data) => {
            if (success && data != null)
            {
                // 缓存数据
                cachedAssemblyData[startPartName] = data;
                callback?.Invoke(data);
            }
            else
            {
                // 使用本地备用数据
                if (useLocalFallback)
                {
                    Debug.LogWarning($"[ExternalSystemDataProvider] 外部系统请求失败，使用本地备用数据");
                    var fallbackData = LoadFallbackData(startPartName);
                    callback?.Invoke(fallbackData);
                }
                else
                {
                    Debug.LogError($"[ExternalSystemDataProvider] 无法获取装配数据: {startPartName}");
                    callback?.Invoke(new List<AssemblyStepData>());
                }
            }
        }));
    }

    /// <summary>
    /// 测试外部系统连接
    /// </summary>
    public IEnumerator TestConnection(Action<bool> callback)
    {
        Debug.Log("[ExternalSystemDataProvider] 测试外部系统连接...");

        using (UnityEngine.Networking.UnityWebRequest request = UnityEngine.Networking.UnityWebRequest.Get(apiEndpoint + "/health"))
        {
            request.timeout = (int)requestTimeout;
            yield return request.SendWebRequest();

            bool connected = request.result == UnityEngine.Networking.UnityWebRequest.Result.Success;
            IsConnected = connected;
            
            Debug.Log($"[ExternalSystemDataProvider] 连接测试结果: {(connected ? "成功" : "失败")}");
            callback?.Invoke(connected);
        }
    }

    /// <summary>
    /// 从外部系统请求装配数据
    /// </summary>
    private IEnumerator RequestAssemblyDataFromExternalSystem(string partName, Action<bool, List<AssemblyStepData>> callback)
    {
        string url = $"{apiEndpoint}/assembly/{partName}";
        
        using (UnityEngine.Networking.UnityWebRequest request = UnityEngine.Networking.UnityWebRequest.Get(url))
        {
            request.timeout = (int)requestTimeout;
            yield return request.SendWebRequest();

            if (request.result == UnityEngine.Networking.UnityWebRequest.Result.Success)
            {
                try
                {
                    // 解析JSON响应
                    var assemblyData = ParseAssemblyDataFromJson(request.downloadHandler.text);
                    callback?.Invoke(true, assemblyData);
                }
                catch (System.Exception e)
                {
                    Debug.LogError($"[ExternalSystemDataProvider] JSON解析失败: {e.Message}");
                    callback?.Invoke(false, null);
                }
            }
            else
            {
                Debug.LogError($"[ExternalSystemDataProvider] 请求失败: {request.error}");
                callback?.Invoke(false, null);
            }
        }
    }

    /// <summary>
    /// 解析JSON格式的装配数据
    /// </summary>
    private List<AssemblyStepData> ParseAssemblyDataFromJson(string jsonData)
    {
        // 这里需要根据外部系统的JSON格式进行解析
        // 示例实现，您需要根据实际的JSON结构调整
        var steps = new List<AssemblyStepData>();
        
        try
        {
            var jsonObject = SimpleJSON.JSON.Parse(jsonData);
            var stepsArray = jsonObject["steps"];
            
            foreach (JSONNode stepNode in stepsArray.AsArray)
            {
                var step = new AssemblyStepData
                {
                    movingPartName = stepNode["movingPart"],
                    targetPartName = stepNode["targetPart"],
                    movingPartRefPoint = stepNode["movingRefPoint"],
                    targetPartRefPoint = stepNode["targetRefPoint"],
                    connectionType = stepNode["connectionType"],
                    fastener = new AssemblyStepData.FastenerData
                    {
                        type = stepNode["fastener"]["type"],
                        screwType = stepNode["fastener"]["screwType"],
                        nutType = stepNode["fastener"]["nutType"]
                    },
                    additionalMountPoints = new List<AssemblyStepData.AdditionalMountPointData>()
                };

                // 解析额外装配点
                if (stepNode["additionalMountPoints"] != null)
                {
                    foreach (JSONNode mountPointNode in stepNode["additionalMountPoints"].AsArray)
                    {
                        step.additionalMountPoints.Add(new AssemblyStepData.AdditionalMountPointData
                        {
                            partName = mountPointNode["partName"],
                            mountPoint = mountPointNode["mountPoint"],
                            screwType = mountPointNode["screwType"],
                            nutType = mountPointNode["nutType"]
                        });
                    }
                }

                steps.Add(step);
            }
        }
        catch (System.Exception e)
        {
            Debug.LogError($"[ExternalSystemDataProvider] JSON解析错误: {e.Message}");
        }
        
        return steps;
    }

    /// <summary>
    /// 加载本地备用数据
    /// </summary>
    private List<AssemblyStepData> LoadFallbackData(string partName)
    {
        if (fallbackDataFile == null)
        {
            Debug.LogWarning("[ExternalSystemDataProvider] 没有配置本地备用数据文件");
            return new List<AssemblyStepData>();
        }

        try
        {
            return ParseAssemblyDataFromJson(fallbackDataFile.text);
        }
        catch (System.Exception e)
        {
            Debug.LogError($"[ExternalSystemDataProvider] 本地备用数据解析失败: {e.Message}");
            return new List<AssemblyStepData>();
        }
    }

    /// <summary>
    /// 手动设置装配数据（用于测试或外部调用）
    /// </summary>
    public void SetAssemblyData(string partName, List<AssemblyStepData> data)
    {
        cachedAssemblyData[partName] = data;
        Debug.Log($"[ExternalSystemDataProvider] 手动设置装配数据: {partName}, 步骤数: {data.Count}");
    }

    /// <summary>
    /// 清除缓存数据
    /// </summary>
    public void ClearCache()
    {
        cachedAssemblyData.Clear();
        Debug.Log("[ExternalSystemDataProvider] 缓存数据已清除");
    }
}
