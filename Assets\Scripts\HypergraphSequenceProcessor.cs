using UnityEngine;
using System.Collections.Generic;
using System.Collections;
using SimpleJSON;

/// <summary>
/// 超图序列和Agent序列处理器
/// 处理包含子图切换和工具调用的装配序列
/// </summary>
public class HypergraphSequenceProcessor : MonoBehaviour
{
    [Header("组件引用")]
    [SerializeField] private Neo4jAssemblyController assemblyController;
    [SerializeField] private AssemblyAnimationManager animationManager;
    [SerializeField] private AgentTools agentTools;
    
    [Header("序列数据设置")]
    [SerializeField] private TextAsset hypergraphSequenceFile; // 超图序列文件
    [SerializeField] private TextAsset agentSequenceFile; // Agent序列文件
    [SerializeField] private SequenceType currentSequenceType = SequenceType.Hypergraph;
    
    [Header("执行设置")]
    [SerializeField] private KeyCode executeKey = KeyCode.Space;
    [SerializeField] private KeyCode resetKey = KeyCode.R;
    [SerializeField] private KeyCode switchSequenceKey = KeyCode.Tab;
    [SerializeField] private float stepDelay = 1.0f;
    
    [Header("调试设置")]
    [SerializeField] private bool enableDebugLog = true;
    [SerializeField] private bool showStepDetails = true;
    
    // 序列类型枚举
    public enum SequenceType
    {
        Hypergraph,  // 超图序列
        Agent        // Agent序列
    }
    
    // 序列步骤数据结构
    [System.Serializable]
    public class SequenceStep
    {
        public string type;
        public string subgraphId;
        public string description;
        public string toolName;
        public JSONNode parameters;
        
        // 装配步骤数据
        public string movingPartName;
        public string targetPartName;
        public string movingPartRefPoint;
        public string targetPartRefPoint;
        public string connectionType;
        public string fastenerType;
        public string screwType;
        public string nutType;
        public List<string> additionalMountPoints;
    }
    
    // 内部状态
    private List<SequenceStep> sequenceSteps = new List<SequenceStep>();
    private int currentStepIndex = 0;
    private bool isExecuting = false;
    private bool isSequenceLoaded = false;

    void Start()
    {
        InitializeProcessor();
        LoadCurrentSequence();
    }

    void Update()
    {
        if (Input.GetKeyDown(executeKey) && isSequenceLoaded && !isExecuting)
        {
            ExecuteNextStep();
        }
        
        if (Input.GetKeyDown(resetKey))
        {
            ResetSequence();
        }
        
        if (Input.GetKeyDown(switchSequenceKey))
        {
            SwitchSequenceType();
        }
    }

    /// <summary>
    /// 初始化处理器
    /// </summary>
    private void InitializeProcessor()
    {
        // 自动查找组件
        if (assemblyController == null)
            assemblyController = FindObjectOfType<Neo4jAssemblyController>();
            
        if (animationManager == null)
            animationManager = FindObjectOfType<AssemblyAnimationManager>();
            
        if (agentTools == null)
            agentTools = FindObjectOfType<AgentTools>();
        
        // 验证组件
        if (assemblyController == null)
        {
            Debug.LogError("[HypergraphSequenceProcessor] 未找到Neo4jAssemblyController组件！");
            enabled = false;
            return;
        }
        
        if (animationManager == null)
        {
            Debug.LogError("[HypergraphSequenceProcessor] 未找到AssemblyAnimationManager组件！");
            enabled = false;
            return;
        }
        
        if (agentTools == null)
        {
            Debug.LogError("[HypergraphSequenceProcessor] 未找到AgentTools组件！");
            enabled = false;
            return;
        }
        
        LogDebug("超图序列处理器初始化完成");
    }

    /// <summary>
    /// 加载当前序列
    /// </summary>
    private void LoadCurrentSequence()
    {
        TextAsset sequenceFile = currentSequenceType == SequenceType.Hypergraph ? 
            hypergraphSequenceFile : agentSequenceFile;
            
        if (sequenceFile == null)
        {
            Debug.LogWarning($"[HypergraphSequenceProcessor] {currentSequenceType} 序列文件未设置");
            return;
        }
        
        LogDebug($"加载 {currentSequenceType} 序列: {sequenceFile.name}");
        ParseSequenceData(sequenceFile.text);
    }

    /// <summary>
    /// 解析序列数据
    /// </summary>
    /// <param name="jsonData">JSON格式的序列数据</param>
    private void ParseSequenceData(string jsonData)
    {
        try
        {
            sequenceSteps.Clear();
            
            JSONNode rootNode = JSON.Parse(jsonData);
            JSONArray sequenceArray = rootNode["sequence"].AsArray;
            
            foreach (JSONNode stepNode in sequenceArray)
            {
                SequenceStep step = new SequenceStep();
                step.type = stepNode["type"];
                step.description = stepNode["description"];
                
                // 根据步骤类型解析不同的数据
                switch (step.type)
                {
                    case "subgraph_switch":
                        step.subgraphId = stepNode["subgraphId"];
                        break;
                        
                    case "narrative_tool_call":
                        step.toolName = stepNode["tool_name"];
                        step.parameters = stepNode["parameters"];
                        break;
                        
                    case "assembly":
                        ParseAssemblyStep(stepNode, step);
                        break;
                }
                
                sequenceSteps.Add(step);
            }
            
            isSequenceLoaded = true;
            currentStepIndex = 0;
            
            LogDebug($"序列解析完成，共 {sequenceSteps.Count} 个步骤");
            
            if (showStepDetails)
            {
                ShowSequenceOverview();
            }
        }
        catch (System.Exception e)
        {
            Debug.LogError($"[HypergraphSequenceProcessor] 解析序列数据失败: {e.Message}");
        }
    }

    /// <summary>
    /// 解析装配步骤数据
    /// </summary>
    private void ParseAssemblyStep(JSONNode stepNode, SequenceStep step)
    {
        step.movingPartName = stepNode["movingPartName"];
        step.targetPartName = stepNode["targetPartName"];
        step.movingPartRefPoint = stepNode["movingPartRefPoint"];
        step.targetPartRefPoint = stepNode["targetPartRefPoint"];
        step.connectionType = stepNode["connectionType"];
        step.fastenerType = stepNode["fastenerType"];
        step.screwType = stepNode["screwType"];
        step.nutType = stepNode["nutType"];
        
        // 解析额外装配点
        step.additionalMountPoints = new List<string>();
        if (stepNode["additionalMountPoints"] != null)
        {
            foreach (JSONNode mountPoint in stepNode["additionalMountPoints"].AsArray)
            {
                step.additionalMountPoints.Add(mountPoint.Value);
            }
        }
    }

    /// <summary>
    /// 执行下一个步骤
    /// </summary>
    public void ExecuteNextStep()
    {
        if (currentStepIndex >= sequenceSteps.Count)
        {
            LogDebug("序列执行完成！");
            return;
        }
        
        SequenceStep step = sequenceSteps[currentStepIndex];
        LogDebug($"执行步骤 {currentStepIndex + 1}/{sequenceSteps.Count}: {step.type}");
        
        StartCoroutine(ExecuteStepCoroutine(step));
    }

    /// <summary>
    /// 执行步骤协程
    /// </summary>
    private IEnumerator ExecuteStepCoroutine(SequenceStep step)
    {
        isExecuting = true;
        
        switch (step.type)
        {
            case "subgraph_switch":
                ExecuteSubgraphSwitch(step);
                break;
                
            case "narrative_tool_call":
                ExecuteToolCall(step);
                break;
                
            case "assembly":
                yield return ExecuteAssemblyStep(step);
                break;
                
            default:
                Debug.LogWarning($"未知的步骤类型: {step.type}");
                break;
        }
        
        currentStepIndex++;
        isExecuting = false;
        
        yield return new WaitForSeconds(stepDelay);
    }

    /// <summary>
    /// 执行子图切换
    /// </summary>
    private void ExecuteSubgraphSwitch(SequenceStep step)
    {
        LogDebug($"🔄 执行子图切换: {step.subgraphId}");
        agentTools.SwitchSubgraph(step.subgraphId);
    }

    /// <summary>
    /// 执行工具调用
    /// </summary>
    private void ExecuteToolCall(SequenceStep step)
    {
        LogDebug($"🔧 执行工具调用: {step.toolName}");
        
        switch (step.toolName)
        {
            case "文本ui":
                string text = step.parameters["text"];
                agentTools.ShowTextUI(text);
                break;
                
            case "高亮":
                string targetPart = step.parameters["目标零件"];
                agentTools.HighlightPart(targetPart);
                break;
                
            case "建立层次关系认识":
                // 处理子图高亮顺序
                JSONArray highlightSequence = step.parameters["子图高亮顺序"].AsArray;
                foreach (JSONNode sequence in highlightSequence)
                {
                    List<string> parts = new List<string>();
                    foreach (JSONNode part in sequence.AsArray)
                    {
                        parts.Add(part.Value);
                    }
                    agentTools.HighlightSubgraphSequence(parts.ToArray());
                }
                break;
                
            default:
                Debug.LogWarning($"未知的工具名称: {step.toolName}");
                break;
        }
    }

    /// <summary>
    /// 执行装配步骤
    /// </summary>
    private IEnumerator ExecuteAssemblyStep(SequenceStep step)
    {
        LogDebug($"🔧 执行装配步骤: {step.movingPartName} -> {step.targetPartName}");

        // 将步骤转换为JSON格式，然后通过ReceiveExternalAssemblyData方法发送
        string stepJson = ConvertStepToJson(step);

        // 使用公共方法发送装配数据
        assemblyController.ReceiveExternalAssemblyData($"HypergraphStep_{currentStepIndex}", stepJson);

        // 等待一帧，让装配控制器处理数据
        yield return null;

        // 等待装配动画完成（这里可以根据需要调整等待时间或监听完成事件）
        yield return new WaitForSeconds(2.0f);
    }

    /// <summary>
    /// 将序列步骤转换为JSON格式
    /// </summary>
    private string ConvertStepToJson(SequenceStep step)
    {
        var stepData = new
        {
            steps = new[]
            {
                new
                {
                    movingPart = step.movingPartName,
                    targetPart = step.targetPartName,
                    movingRefPoint = step.movingPartRefPoint,
                    targetRefPoint = step.targetPartRefPoint,
                    connectionType = step.connectionType ?? "DIRECT",
                    fastener = new
                    {
                        type = step.fastenerType ?? "NONE",
                        screwType = step.screwType ?? "",
                        nutType = step.nutType ?? ""
                    },
                    additionalMountPoints = ConvertAdditionalMountPoints(step.additionalMountPoints)
                }
            }
        };

        return SimpleJSON.JSON.Parse(JsonUtility.ToJson(stepData)).ToString();
    }

    /// <summary>
    /// 转换额外装配点为JSON格式
    /// </summary>
    private object[] ConvertAdditionalMountPoints(List<string> additionalMountPoints)
    {
        if (additionalMountPoints == null || additionalMountPoints.Count == 0)
        {
            return new object[0];
        }

        var mountPointsList = new List<object>();

        foreach (string mountPoint in additionalMountPoints)
        {
            // 解析额外装配点格式: "fromPoint,toPoint,connectionType,fastenerType,screwType,nutType"
            string[] parts = mountPoint.Split(',');
            if (parts.Length >= 6)
            {
                mountPointsList.Add(new
                {
                    partName = parts[0].Split('_')[0], // 从 "partName_pointName" 中提取零件名
                    mountPoint = parts[0].Split('_').Length > 1 ? parts[0].Split('_')[1] : "P0",
                    screwType = parts[4],
                    nutType = parts[5]
                });
            }
        }

        return mountPointsList.ToArray();
    }

    /// <summary>
    /// 重置序列
    /// </summary>
    public void ResetSequence()
    {
        currentStepIndex = 0;
        isExecuting = false;
        LogDebug("序列已重置");
    }

    /// <summary>
    /// 切换序列类型
    /// </summary>
    public void SwitchSequenceType()
    {
        currentSequenceType = currentSequenceType == SequenceType.Hypergraph ? 
            SequenceType.Agent : SequenceType.Hypergraph;
            
        LogDebug($"切换到 {currentSequenceType} 序列");
        LoadCurrentSequence();
    }

    /// <summary>
    /// 显示序列概览
    /// </summary>
    private void ShowSequenceOverview()
    {
        LogDebug($"=== {currentSequenceType} 序列概览 ===");
        for (int i = 0; i < sequenceSteps.Count; i++)
        {
            SequenceStep step = sequenceSteps[i];
            string stepInfo = $"步骤 {i + 1}: {step.type}";
            
            if (!string.IsNullOrEmpty(step.subgraphId))
                stepInfo += $" - {step.subgraphId}";
            if (!string.IsNullOrEmpty(step.toolName))
                stepInfo += $" - {step.toolName}";
            if (!string.IsNullOrEmpty(step.movingPartName))
                stepInfo += $" - {step.movingPartName} -> {step.targetPartName}";
                
            LogDebug(stepInfo);
        }
        LogDebug("=== 序列概览结束 ===");
    }

    /// <summary>
    /// 调试日志输出
    /// </summary>
    private void LogDebug(string message)
    {
        if (enableDebugLog)
        {
            Debug.Log($"[HypergraphSequenceProcessor] {message}");
        }
    }

    void OnGUI()
    {
        if (!enableDebugLog) return;
        
        GUILayout.BeginArea(new Rect(10, 350, 400, 200));
        GUILayout.Label($"超图序列处理器 - {currentSequenceType}");
        GUILayout.Space(5);
        
        GUILayout.Label($"当前步骤: {currentStepIndex + 1}/{sequenceSteps.Count}");
        GUILayout.Label($"执行状态: {(isExecuting ? "执行中" : "等待")}");
        GUILayout.Label($"序列状态: {(isSequenceLoaded ? "已加载" : "未加载")}");
        
        GUILayout.Space(10);
        GUILayout.Label("控制:");
        GUILayout.Label($"{executeKey} - 执行下一步");
        GUILayout.Label($"{resetKey} - 重置序列");
        GUILayout.Label($"{switchSequenceKey} - 切换序列类型");
        
        GUILayout.EndArea();
    }
}
