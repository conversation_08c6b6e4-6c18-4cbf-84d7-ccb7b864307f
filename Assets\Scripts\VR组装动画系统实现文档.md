# VR组装动画系统实现文档

## 1. 系统概述

本系统实现了VR环境中螺栓组装的精确动画流程，主要包括U型架对齐、螺丝插入和螺母拧紧三个主要阶段。系统使用Unity引擎开发，通过协程实现平滑动画效果，并提供了多种参数用于调整和优化。

## 2. 核心组件

### 2.1 物体引用

系统通过Transform引用来跟踪和控制所有关键组件：

```csharp
public Transform longU;         // 长U型架（根物体）
public Transform shortU;        // 小U型架（根物体）
public Transform screw;         // 螺丝（根物体）
public Transform nut;           // 螺母（根物体）

public Transform longUHole;     // 长U的孔位参考点（子物体）
public Transform shortUHole;    // 小U的孔位参考点（子物体）
public Transform screwMountPoint; // 螺丝的安装点（子物体）
public Transform nutMountPoint;   // 螺母的安装点（子物体）
```

### 2.2 参数配置

系统提供了多种参数以便调整动画效果：

```csharp
public float screwLength = 0.005f;       // 螺丝螺杆长度
public float nutThickness = 0.001f;      // 螺母厚度
public float animationDuration = 2f;     // 每段动画持续时间
public float initialDistanceFromHole = 0.05f; // 螺丝在延长线上的初始位置到孔位的距离
public float approachDistance = 0.02f;   // 螺丝接近孔位的距离
```

## 3. 装配流程实现

### 3.1 U型架对齐

U型架对齐是装配的第一步，通过计算两个孔位的位置关系来确定：

```csharp
// 计算小U型架的目标位置和旋转
Vector3 localHolePos = shortU.InverseTransformPoint(shortUHole.position);
Vector3 targetPos = longUHole.position - shortU.TransformPoint(localHolePos) + shortU.position;
Quaternion targetRot = CalculateTargetRotation();

// 移动小U型架到目标位置和旋转
yield return StartCoroutine(MoveAndRotate(shortU, targetPos, targetRot, animationDuration, false));
```

**实现原理**：
1. 通过`InverseTransformPoint`计算短U型架的孔位在本地坐标系中的位置
2. 计算出短U型架需要移动到的位置，使其孔位与长U型架孔位对齐
3. 使用`CalculateTargetRotation`计算所需的旋转角度
4. 通过`MoveAndRotate`协程平滑移动和旋转短U型架

### 3.2 螺丝安装

螺丝安装采用了改进的动画路径，分为五个步骤：

```csharp
// 计算关键位置
Vector3 initialPointForMountPoint = longUHole.position - holeDirection * initialDistanceFromHole + Vector3.up * screwInitialElevation;
Vector3 holePoint = longUHole.position + Vector3.up * screwFinalElevation;
Vector3 approachPointForMountPoint = longUHole.position - holeDirection * approachDistance + Vector3.up * screwFinalElevation;

// 1. 移动螺丝到延长线上的初始位置
yield return StartCoroutine(MoveAndRotateByMountPoint(screw, screwMountPoint, initialPointForMountPoint, initialRotation, animationDuration));

// 2. 在延长线上调整螺丝方向
yield return StartCoroutine(RotateInPlace(screw, screwMountPoint, finalRotation, screwRotationDuration));

// 3. 移动螺丝到孔位附近
yield return StartCoroutine(MoveByMountPoint(screw, screwMountPoint, approachPointForMountPoint, animationDuration * 0.7f));

// 4. 精确移动螺丝到孔位
yield return StartCoroutine(MoveByMountPoint(screw, screwMountPoint, holePoint, animationDuration * 0.5f));

// 5. 插入螺丝
Vector3 screwInsertedPosition = holePoint + holeDirection * screwLength;
yield return StartCoroutine(MoveByMountPoint(screw, screwMountPoint, screwInsertedPosition, animationDuration));
```

**实现原理**：
1. 将螺丝挂载点(`screwMountPoint`)作为精确控制点
2. 首先移动螺丝到孔位延长线上的位置
3. 调整螺丝朝向与孔位方向对齐
4. 先移动到孔位附近，然后精确对齐到孔位位置
5. 沿孔位方向移动螺丝，模拟插入过程

### 3.3 螺母安装

螺母安装实现了更自然的移动和旋转动画：

```csharp
// 计算螺丝尾部位置和螺母初始位置
Vector3 screwEndPosition = longUHole.position + holeDirection * screwLength;
Vector3 nutStartPosition = screwEndPosition + Vector3.up * 0.03f + holeDirection * (initialDistanceFromHole * 0.3f);

// 1. 移动螺母到初始位置
yield return StartCoroutine(MoveAndRotateByMountPoint(nut, nutMountPoint, nutStartPosition, nutStartRotation, animationDuration));

// 2. 移动螺母到螺丝尾部
yield return StartCoroutine(MoveByMountPoint(nut, nutMountPoint, screwEndPosition, animationDuration * 0.7f));

// 3. 旋转螺母（拧紧过程）
yield return StartCoroutine(RotateInPlace(nut, nutMountPoint, targetRotation, animationDuration * 0.6f));

// 4. 移动螺母到拧紧位置
Vector3 nutTightenedPosition = screwEndPosition - holeDirection * (nutThickness * 0.7f);
yield return StartCoroutine(MoveByMountPoint(nut, nutMountPoint, nutTightenedPosition, animationDuration * 0.4f));
```

**实现原理**：
1. 计算螺丝尾部位置（螺母的目标位置）
2. 定义螺母的初始位置（略高于螺丝尾部）
3. 移动螺母到初始位置并调整方向
4. 移动螺母到螺丝尾部
5. 原地旋转螺母，模拟拧紧动作
6. 移动螺母向内一小段距离，完成拧紧

## 4. 关键技术

### 4.1 挂载点控制

系统使用挂载点（MountPoint）来精确控制部件位置：

```csharp
void UpdateMountPointPosition(Transform target, Transform mountPoint)
{
    // 获取从父物体到挂载点的本地坐标偏移
    Vector3 localOffset = target.InverseTransformPoint(mountPoint.position);
    
    // 将挂载点位置设置为当前物体位置加上本地偏移的世界坐标转换
    mountPoint.position = target.TransformPoint(localOffset);
    
    // 同步旋转
    mountPoint.rotation = target.rotation;
}
```

**实现原理**：
1. 挂载点是物体上的特定点，用于精确对齐
2. 通过维护物体与挂载点之间的相对关系，确保移动和旋转时的精确性
3. 挂载点作为目标位置，而不是直接移动物体本身

### 4.2 原地旋转

为了实现螺丝和螺母的精确旋转，系统实现了原地旋转方法：

```csharp
IEnumerator RotateInPlace(Transform target, Transform mountPoint, Quaternion targetRotation, float duration)
{
    // 记录当前位置和旋转
    Vector3 fixedPosition = mountPoint.position;
    Quaternion startRotation = target.rotation;
    
    // 旋转动画
    float elapsed = 0f;
    while (elapsed < duration)
    {
        elapsed += Time.deltaTime;
        float t = Mathf.SmoothStep(0f, 1f, elapsed / duration);
        
        // 保持挂载点位置不变
        mountPoint.position = fixedPosition;
        
        // 应用旋转插值
        target.rotation = Quaternion.Slerp(startRotation, targetRotation, t);
        
        // 计算旋转后的偏移
        Vector3 rotatedOffset = target.rotation * Quaternion.Inverse(startRotation) * initialOffset;
        
        // 更新物体位置以维持挂载点位置不变
        target.position = mountPoint.position + rotatedOffset;
        
        yield return null;
    }
}
```

**实现原理**：
1. 保持挂载点位置不变，只旋转物体本身
2. 随着物体旋转，计算并应用新的偏移，确保挂载点位置保持不变
3. 使用四元数插值实现平滑旋转

### 4.3 平滑动画控制

系统使用SmoothStep函数来实现平滑的动画效果：

```csharp
float t = Mathf.SmoothStep(0f, 1f, elapsed / duration);
target.position = Vector3.Lerp(startPos, adjustedTargetPos, t);
target.rotation = Quaternion.Slerp(startRot, targetRot, t);
```

**实现原理**：
1. 使用SmoothStep函数产生非线性的平滑过渡，使动画开始和结束更为自然
2. 使用Vector3.Lerp进行位置的线性插值
3. 使用Quaternion.Slerp进行旋转的球面线性插值

## 5. 调试功能

系统提供了多种调试功能以便开发和调整：

### 5.1 Gizmos可视化

```csharp
void OnDrawGizmos()
{
    if (longUHole)
    {
        Gizmos.color = Color.blue;
        Vector3 holeDir = GetAxisDirection(longUHole);
        Gizmos.DrawRay(longUHole.position, holeDir * 0.1f);
        Gizmos.DrawWireSphere(longUHole.position, 0.01f);
    }
    // ...其他Gizmos绘制代码
}
```

### 5.2 调试信息输出

```csharp
public void PrintDebugInfo()
{
    Debug.Log("==== 调试信息 ====");
    if (longU) Debug.Log($"长U位置: {longU.position}, 旋转: {longU.rotation.eulerAngles}, 缩放: {longU.lossyScale}");
    // ...其他调试信息输出
}
```

## 6. 总结

本VR组装动画系统通过精确控制物体的移动和旋转，实现了真实的螺栓组装过程。系统的核心特点包括：

1. **精确控制**：使用挂载点精确控制部件位置和旋转
2. **自然动画**：使用改进的移动路径和平滑过渡创造自然动画效果
3. **模块化设计**：将组装流程分解为多个协程，便于调整和扩展
4. **丰富参数**：提供多种参数用于调整动画效果
5. **调试功能**：提供Gizmos可视化和调试信息输出

通过这些实现，系统能够在VR环境中呈现出逼真的装配过程，为用户提供沉浸式的装配体验。 