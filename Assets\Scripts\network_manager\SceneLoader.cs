using UnityEngine;
using UnityEngine.SceneManagement;

/// <summary>
/// 全局场景加载器
/// 监听由EventManager广播的场景加载事件，并执行场景切换。
/// 这是一个持久化组件，应放置在start场景中，以确保在整个应用生命周期内都能响应事件。
/// </summary>
public class SceneLoader : MonoBehaviour
{
    void Start()
    {
        // 将此组件标记为在场景切换时不被销毁
        DontDestroyOnLoad(this.gameObject);
        
        // 注册对“请求加载场景”事件的监听
        EventManager.OnLoadSceneRequested += HandleLoadSceneRequest;
        
        Debug.Log("[SceneLoader] 初始化完成，已开始监听场景加载事件。");
    }

    private void OnDestroy()
    {
        // 在对象被销毁时，取消事件监听，防止内存泄漏
        EventManager.OnLoadSceneRequested -= HandleLoadSceneRequest;
    }

    /// <summary>
    /// 处理场景加载请求的核心方法
    /// </summary>
    /// <param name="sceneName">要加载的场景名称，必须与Build Settings中的名称一致</param>
    private void HandleLoadSceneRequest(string sceneName)
    {
        if (string.IsNullOrEmpty(sceneName))
        {
            Debug.LogWarning("[SceneLoader] 收到的场景名称为空，已忽略加载请求。");
            return;
        }

        Debug.Log($"[SceneLoader] 收到加载场景请求，正在加载场景: {sceneName}");

        try
        {
            // 执行场景加载
            SceneManager.LoadScene(sceneName);
        }
        catch (System.Exception e)
        {
            // 如果场景不存在或未添加到Build Settings，这里会捕获到异常
            Debug.LogError($"[SceneLoader] 加载场景 '{sceneName}' 失败! " +
                           $"请确保该场景已添加到File > Build Settings中，并且名称完全匹配。错误信息: {e.Message}");
            // TODO: (未来) 在这里可以向服务器发送一个加载失败的回执
        }
    }
} 