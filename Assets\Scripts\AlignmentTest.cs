using UnityEngine;
using System.Collections;

/**
 * 对齐测试脚本
 *
 * 用于测试不同零件之间的对齐功能
 * 可以在编辑器中设置源零件和目标零件，以及它们的参考点索引
 */
public class AlignmentTest : MonoBehaviour
{
    [Header("测试零件")]
    [SerializeField] private AssemblyPart sourcePart; // 源零件（将被移动）
    [SerializeField] private AssemblyPart targetPart; // 目标零件（固定不动）

    [Header("参考点选择")]
    [SerializeField] private bool useNamedReferences = false; // 是否使用命名参考点
    [SerializeField] private string sourceRefName = "P1"; // 源零件的参考点名称
    [SerializeField] private string targetRefName = "P5"; // 目标零件的参考点名称
    [SerializeField] private int sourceRefIndex = 0; // 源零件的参考点索引
    [SerializeField] private int targetRefIndex = 0; // 目标零件的参考点索引

    [Header("测试控制")]
    [SerializeField] private float alignmentDuration = 1.0f; // 对齐动画持续时间
    [SerializeField] private KeyCode testKey = KeyCode.T; // 测试快捷键
    [SerializeField] private KeyCode resetKey = KeyCode.R; // 重置快捷键
    [SerializeField] private KeyCode validateKey = KeyCode.V; // 验证参考点快捷键
    [SerializeField] private KeyCode analyzeKey = KeyCode.A; // 分析旋转约束快捷键

    [Header("调试选项")]
    [SerializeField] private bool showDebugInfo = true; // 是否显示调试信息

    private AssemblyAnimationManager animationManager;
    private Vector3 sourceOriginalPosition; // 源零件的原始位置
    private Quaternion sourceOriginalRotation; // 源零件的原始旋转

    void Start()
    {
        animationManager = new AssemblyAnimationManager();

        // 记录源零件的原始位置和旋转
        if (sourcePart != null)
        {
            sourceOriginalPosition = sourcePart.PartTransform.position;
            sourceOriginalRotation = sourcePart.PartTransform.rotation;
        }
    }

    void Update()
    {
        // 按下测试键执行对齐
        if (Input.GetKeyDown(testKey))
        {
            TestAlignment();
        }

        // 按下重置键恢复源零件的原始位置和旋转
        if (Input.GetKeyDown(resetKey))
        {
            ResetSourcePart();
        }

        // 按下验证键验证参考点
        if (Input.GetKeyDown(validateKey))
        {
            ValidateReferencesPoints();
        }

        // 按下分析键分析旋转约束
        if (Input.GetKeyDown(analyzeKey))
        {
            AnalyzeRotationConstraints();
        }
    }

    /**
     * 验证源零件和目标零件的参考点
     */
    private void ValidateReferencesPoints()
    {
        Debug.Log("开始验证参考点...");

        if (sourcePart != null)
        {
            Debug.Log($"验证源零件 {sourcePart.PartName} 的参考点:");
            sourcePart.ValidateReferencePoints();

            // 打印详细的参考点轴向信息
            PrintReferencePointAxes(sourcePart);
        }
        else
        {
            Debug.LogWarning("未设置源零件！");
        }

        if (targetPart != null)
        {
            Debug.Log($"验证目标零件 {targetPart.PartName} 的参考点:");
            targetPart.ValidateReferencePoints();

            // 打印详细的参考点轴向信息
            PrintReferencePointAxes(targetPart);
        }
        else
        {
            Debug.LogWarning("未设置目标零件！");
        }

        Debug.Log("参考点验证完成。");
    }

    /**
     * 打印参考点的轴向信息
     */
    private void PrintReferencePointAxes(AssemblyPart part)
    {
        if (part == null) return;

        Debug.Log($"=== {part.PartName} 参考点轴向信息 ===");

        for (int i = 0; i < part.ReferencePointCount; i++)
        {
            Transform refPoint = part.GetReferencePoint(i);
            if (refPoint == null) continue;

            Debug.Log($"参考点 {refPoint.name} (索引 {i}):");
            Debug.Log($"  位置: {refPoint.position}");
            Debug.Log($"  旋转: {refPoint.rotation.eulerAngles}");
            Debug.Log($"  Forward轴 (Z): {refPoint.forward} (世界坐标)");
            Debug.Log($"  Up轴 (Y): {refPoint.up} (世界坐标)");
            Debug.Log($"  Right轴 (X): {refPoint.right} (世界坐标)");

            // 计算参考点相对于零件的本地位置和旋转
            Vector3 localPos = part.PartTransform.InverseTransformPoint(refPoint.position);
            Quaternion localRot = Quaternion.Inverse(part.PartTransform.rotation) * refPoint.rotation;

            Debug.Log($"  本地位置: {localPos} (相对于零件)");
            Debug.Log($"  本地旋转: {localRot.eulerAngles} (相对于零件)");

            // 计算参考点与零件的旋转差异
            float rotDiff = Quaternion.Angle(part.PartTransform.rotation, refPoint.rotation);
            Debug.Log($"  与零件的旋转差异: {rotDiff}°");
        }

        Debug.Log($"=== {part.PartName} 参考点信息结束 ===");
    }

    /**
     * 测试对齐功能
     */
    public void TestAlignment()
    {
        if (sourcePart == null || targetPart == null)
        {
            Debug.LogError("请设置源零件和目标零件！");
            return;
        }

        Debug.Log($"开始测试对齐 - 源零件: {sourcePart.PartName}, 目标零件: {targetPart.PartName}");

        // 获取参考点
        Transform sourceRef;
        Transform targetRef;

        // 根据设置选择使用名称或索引获取参考点
        if (useNamedReferences)
        {
            // 按名称获取参考点
            sourceRef = sourcePart.GetReferencePointByName(sourceRefName);
            targetRef = targetPart.GetReferencePointByName(targetRefName);

            if (sourceRef == null || targetRef == null)
            {
                Debug.LogError($"参考点无效！源名称: {sourceRefName}, 目标名称: {targetRefName}");
                return;
            }

            // 查找索引，用于调用AlignParts方法
            int actualSourceIndex = GetReferencePointIndex(sourcePart, sourceRef);
            int actualTargetIndex = GetReferencePointIndex(targetPart, targetRef);

            if (actualSourceIndex < 0 || actualTargetIndex < 0)
            {
                Debug.LogError("无法确定参考点索引！");
                return;
            }

            if (showDebugInfo)
            {
                Debug.Log($"源参考点: {sourceRef.name}, 位置: {sourceRef.position}, 旋转: {sourceRef.rotation.eulerAngles}");
                Debug.Log($"目标参考点: {targetRef.name}, 位置: {targetRef.position}, 旋转: {targetRef.rotation.eulerAngles}");
                Debug.Log($"源参考点索引: {actualSourceIndex}, 目标参考点索引: {actualTargetIndex}");
            }

            // 调用对齐方法
            StartCoroutine(animationManager.AlignParts(sourcePart, targetPart, actualSourceIndex, actualTargetIndex, alignmentDuration));
        }
        else
        {
            // 按索引获取参考点
            sourceRef = sourcePart.GetReferencePoint(sourceRefIndex);
            targetRef = targetPart.GetReferencePoint(targetRefIndex);

            if (sourceRef == null || targetRef == null)
            {
                Debug.LogError($"参考点无效！源索引: {sourceRefIndex}, 目标索引: {targetRefIndex}");
                return;
            }

            if (showDebugInfo)
            {
                Debug.Log($"源参考点: {sourceRef.name}, 位置: {sourceRef.position}, 旋转: {sourceRef.rotation.eulerAngles}");
                Debug.Log($"目标参考点: {targetRef.name}, 位置: {targetRef.position}, 旋转: {targetRef.rotation.eulerAngles}");
            }

            // 调用对齐方法
            StartCoroutine(animationManager.AlignParts(sourcePart, targetPart, sourceRefIndex, targetRefIndex, alignmentDuration));
        }
    }

    /**
     * 获取参考点在数组中的索引
     */
    private int GetReferencePointIndex(AssemblyPart part, Transform refPoint)
    {
        if (part == null || refPoint == null) return -1;

        // 遍历参考点数组查找匹配
        for (int i = 0; i < part.ReferencePointCount; i++)
        {
            if (part.GetReferencePoint(i) == refPoint)
            {
                return i;
            }
        }

        return -1;
    }

    /**
     * 重置源零件到原始位置和旋转
     */
    public void ResetSourcePart()
    {
        if (sourcePart == null) return;

        // 一次性设置位置和旋转，避免多次更新变换
        Transform transform = sourcePart.PartTransform;
        transform.SetPositionAndRotation(sourceOriginalPosition, sourceOriginalRotation);

        Debug.Log($"已重置 {sourcePart.PartName} 到原始位置和旋转");
    }

    /**
     * 分析参考点的旋转约束
     */
    public void AnalyzeRotationConstraints()
    {
        if (sourcePart == null || targetPart == null)
        {
            Debug.LogError("请设置源零件和目标零件！");
            return;
        }

        Transform sourceRef = sourcePart.GetReferencePoint(sourceRefIndex);
        Transform targetRef = targetPart.GetReferencePoint(targetRefIndex);

        if (sourceRef == null || targetRef == null)
        {
            Debug.LogError("参考点无效！");
            return;
        }

        Debug.Log("========== 旋转约束分析 ==========");
        Debug.Log($"分析零件: {sourcePart.PartName} -> {targetPart.PartName}");
        Debug.Log($"参考点: {sourceRef.name} -> {targetRef.name}");

        // 分析参考点的轴向
        Debug.Log("--- 参考点轴向 ---");
        Debug.Log($"源参考点 Forward: {sourceRef.forward}, Up: {sourceRef.up}, Right: {sourceRef.right}");
        Debug.Log($"目标参考点 Forward: {targetRef.forward}, Up: {targetRef.up}, Right: {targetRef.right}");

        // 计算轴向对齐情况
        float forwardAlignment = Vector3.Dot(sourceRef.forward, targetRef.forward);
        float upAlignment = Vector3.Dot(sourceRef.up, targetRef.up);
        float rightAlignment = Vector3.Dot(sourceRef.right, targetRef.right);

        Debug.Log($"轴向对齐度 - Forward: {forwardAlignment:F3}, Up: {upAlignment:F3}, Right: {rightAlignment:F3}");

        // 分析旋转自由度
        Debug.Log("--- 旋转自由度分析 ---");

        // 检查是否有轴向完全对齐
        bool hasAlignedAxis = false;
        string alignedAxisName = "";

        if (Mathf.Abs(forwardAlignment) > 0.99f)
        {
            hasAlignedAxis = true;
            alignedAxisName = "Forward (Z)";
        }
        else if (Mathf.Abs(upAlignment) > 0.99f)
        {
            hasAlignedAxis = true;
            alignedAxisName = "Up (Y)";
        }
        else if (Mathf.Abs(rightAlignment) > 0.99f)
        {
            hasAlignedAxis = true;
            alignedAxisName = "Right (X)";
        }

        if (hasAlignedAxis)
        {
            Debug.Log($"检测到轴向对齐: {alignedAxisName}");
            Debug.Log("警告: 当一个轴完全对齐时，绕该轴的旋转存在自由度，可能导致对齐结果有歧义。");
        }
        else
        {
            Debug.Log("没有检测到完全对齐的轴，旋转约束较为完整。");
        }

        // 计算当前旋转与目标旋转的差异
        Quaternion currentRot = sourceRef.rotation;
        Quaternion targetRot = targetRef.rotation;
        float rotationDifference = Quaternion.Angle(currentRot, targetRot);

        Debug.Log($"当前旋转与目标旋转的角度差: {rotationDifference}°");

        // 分析参考点位置关系
        Vector3 positionDifference = targetRef.position - sourceRef.position;
        float distance = positionDifference.magnitude;

        Debug.Log("--- 位置关系分析 ---");
        Debug.Log($"参考点距离: {distance}");
        Debug.Log($"位置差向量: {positionDifference}");

        // 检查位置差向量是否与某个轴对齐
        float forwardPosAlignment = Vector3.Dot(positionDifference.normalized, sourceRef.forward);
        float upPosAlignment = Vector3.Dot(positionDifference.normalized, sourceRef.up);
        float rightPosAlignment = Vector3.Dot(positionDifference.normalized, sourceRef.right);

        Debug.Log($"位置差与轴的对齐度 - Forward: {forwardPosAlignment:F3}, Up: {upPosAlignment:F3}, Right: {rightPosAlignment:F3}");

        // 总结分析结果
        Debug.Log("--- 分析结论 ---");
        if (hasAlignedAxis && Mathf.Abs(Vector3.Dot(positionDifference.normalized, sourceRef.TransformDirection(Vector3.forward))) > 0.9f)
        {
            Debug.Log("高风险情况: 参考点轴向与位置差向量接近平行，且存在轴向对齐。这种情况下单点对齐很可能产生歧义结果。");
        }
        else if (hasAlignedAxis)
        {
            Debug.Log("中等风险: 存在轴向对齐，但位置差向量与轴向不平行。可能在某些情况下产生歧义。");
        }
        else
        {
            Debug.Log("低风险: 没有检测到明显的轴向对齐，单点对齐可能相对稳定。");
        }

        Debug.Log("========== 旋转约束分析结束 ==========");
    }

    /**
     * 在场景视图中绘制调试信息
     */
    void OnDrawGizmos()
    {
        if (!showDebugInfo) return;

        // 绘制源零件和目标零件之间的连线
        if (sourcePart != null && targetPart != null)
        {
            Transform sourceRef = sourcePart.GetReferencePoint(sourceRefIndex);
            Transform targetRef = targetPart.GetReferencePoint(targetRefIndex);

            if (sourceRef != null && targetRef != null)
            {
                // 绘制连接线
                Gizmos.color = Color.yellow;
                Gizmos.DrawLine(sourceRef.position, targetRef.position);

                // 绘制参考点
                Gizmos.color = Color.red;
                Gizmos.DrawSphere(sourceRef.position, 0.01f);
                Gizmos.color = Color.green;
                Gizmos.DrawSphere(targetRef.position, 0.01f);

                // 绘制参考点的方向
                float axisLength = 0.05f;

                // 源参考点轴向
                Gizmos.color = Color.blue; // Z轴 (Forward)
                Gizmos.DrawRay(sourceRef.position, sourceRef.forward * axisLength);
                Gizmos.color = Color.green; // Y轴 (Up)
                Gizmos.DrawRay(sourceRef.position, sourceRef.up * axisLength);
                Gizmos.color = Color.red; // X轴 (Right)
                Gizmos.DrawRay(sourceRef.position, sourceRef.right * axisLength);

                // 目标参考点轴向
                Gizmos.color = Color.blue; // Z轴 (Forward)
                Gizmos.DrawRay(targetRef.position, targetRef.forward * axisLength);
                Gizmos.color = Color.green; // Y轴 (Up)
                Gizmos.DrawRay(targetRef.position, targetRef.up * axisLength);
                Gizmos.color = Color.red; // X轴 (Right)
                Gizmos.DrawRay(targetRef.position, targetRef.right * axisLength);
            }
        }
    }
}
