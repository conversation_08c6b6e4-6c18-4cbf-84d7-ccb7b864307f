using System.Collections.Generic;
using UnityEngine;
using System.IO;
using System;

namespace Woz.Logging
{
    /// <summary>
    /// 集中式日志管理器
    /// </summary>
    public class LogManager : MonoBehaviour
    {
        [Header("日志配置")]
        [Tooltip("设置最低日志记录等级，低于此等级的日志将被忽略")]
        [SerializeField] private LogLevel minimumLogLevel = LogLevel.INFO;

        [Header("输出目标")]
        [Tooltip("是否输出到Unity控制台")]
        [SerializeField] private bool logToConsole = true;
        
        [Tooltip("是否输出到文件")]
        [SerializeField] private bool logToFile = true;

        [Header("文件配置")]
        [Tooltip("日志文件名")]
        [SerializeField] private string logFileName = "vr_client_log.txt";

        // 单例实例
        public static LogManager Instance { get; private set; }

        private List<ILogAppender> _appenders;
        private ILogFormatter _formatter;

        private void Awake()
        {
            // 实现单例模式
            if (Instance == null)
            {
                Instance = this;
                DontDestroyOnLoad(gameObject);
                Initialize();
            }
            else if (Instance != this)
            {
                Destroy(gameObject);
            }
        }

        private void Initialize()
        {
            _appenders = new List<ILogAppender>();
            _formatter = new DefaultLogFormatter();

            if (logToConsole)
            {
                _appenders.Add(new ConsoleAppender(_formatter));
            }

            if (logToFile)
            {
                string logPath = Path.Combine(Application.persistentDataPath, "Logs", logFileName);
                _appenders.Add(new FileAppender(logPath, _formatter));
            }

            Info("LogManager", "日志系统已初始化。");
        }
        
        private void OnDestroy()
        {
            Info("LogManager", "日志系统正在关闭...");
            foreach (var appender in _appenders)
            {
                appender.Dispose();
            }
            _appenders.Clear();
        }

        /// <summary>
        /// 核心日志记录方法
        /// </summary>
        public void Log(LogLevel level, string source, string message)
        {
            // 检查日志等级
            if (level < minimumLogLevel)
            {
                return;
            }

            LogEntry entry = new LogEntry(level, source, message);
            
            // 将日志分发到所有附加器
            foreach (var appender in _appenders)
            {
                appender.Log(entry);
            }
        }

        // --- 便捷的静态方法 ---
        public static void Debug(string source, string message) => Instance?.Log(LogLevel.DEBUG, source, message);
        public static void Info(string source, string message) => Instance?.Log(LogLevel.INFO, source, message);
        public static void Warning(string source, string message) => Instance?.Log(LogLevel.WARNING, source, message);
        public static void Error(string source, string message) => Instance?.Log(LogLevel.ERROR, source, message);
        public static void Error(string source, string message, Exception ex)
        {
            string fullMessage = $"{message}\nException: {ex.Message}\nStackTrace: {ex.StackTrace}";
            Instance?.Log(LogLevel.ERROR, source, fullMessage);
        }
        public static void Fatal(string source, string message) => Instance?.Log(LogLevel.FATAL, source, message);
    }
} 