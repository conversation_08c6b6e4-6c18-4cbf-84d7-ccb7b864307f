using UnityEngine;

/// <summary>
/// VR装配朝向辅助器
/// 
/// 专门处理装配面朝向摄像机的问题，提供简单的朝向计算方法
/// </summary>
public class VRAssemblyOrientationHelper : MonoBehaviour
{
    [Header("朝向设置")]
    [SerializeField] private bool useNegativeZ = true; // 是否使用-Z轴作为装配面
    [SerializeField] private bool debugOrientation = true;
    
    /// <summary>
    /// 计算让装配面朝向摄像机的旋转（增强调试版本）
    /// </summary>
    public Quaternion CalculateOrientationToCamera(Transform referencePoint, Transform camera)
    {
        if (referencePoint == null || camera == null)
        {
            Debug.LogError("[VRAssemblyOrientationHelper] ❌ 参考点或摄像机为空");
            return Quaternion.identity;
        }

        if (debugOrientation)
        {
            Debug.Log("=== 装配面朝向计算开始 ===");
            Debug.Log($"[VRAssemblyOrientationHelper] 🎯 参考点: {referencePoint.name}");
            Debug.Log($"[VRAssemblyOrientationHelper] 📷 摄像机: {camera.name}");
        }

        Vector3 refPointPosition = referencePoint.position;
        Vector3 cameraPosition = camera.position;

        // 计算从装配点到摄像机的方向
        Vector3 toCameraDirection = (cameraPosition - refPointPosition).normalized;
        float distance = Vector3.Distance(refPointPosition, cameraPosition);

        if (debugOrientation)
        {
            Debug.Log($"[VRAssemblyOrientationHelper] 📍 参考点位置: {refPointPosition}");
            Debug.Log($"[VRAssemblyOrientationHelper] 📍 摄像机位置: {cameraPosition}");
            Debug.Log($"[VRAssemblyOrientationHelper] ➡️ 到摄像机方向: {toCameraDirection}");
            Debug.Log($"[VRAssemblyOrientationHelper] 📏 距离: {distance:F2}m");

            // 显示参考点当前的朝向信息
            Debug.Log($"[VRAssemblyOrientationHelper] 🧭 参考点当前朝向:");
            Debug.Log($"   Forward (+Z): {referencePoint.forward}");
            Debug.Log($"   Back (-Z): {-referencePoint.forward}");
            Debug.Log($"   Up (+Y): {referencePoint.up}");
            Debug.Log($"   Right (+X): {referencePoint.right}");
            Debug.Log($"   当前旋转: {referencePoint.rotation.eulerAngles}");
        }

        // 根据设置选择装配面方向
        Vector3 assemblyFaceDirection = useNegativeZ ? -referencePoint.forward : referencePoint.forward;

        if (debugOrientation)
        {
            Debug.Log($"[VRAssemblyOrientationHelper] ⚙️ 装配面设置:");
            Debug.Log($"   使用{(useNegativeZ ? "-Z轴" : "+Z轴")}作为装配面");
            Debug.Log($"   当前装配面方向: {assemblyFaceDirection}");

            // 计算当前装配面与摄像机的对齐度
            float currentAlignment = Vector3.Dot(assemblyFaceDirection, toCameraDirection);
            Debug.Log($"   当前装配面对齐度: {currentAlignment:F3} (1.0为完全对齐)");
        }

        // 计算需要的旋转
        Quaternion targetRotation = Quaternion.LookRotation(toCameraDirection, Vector3.up);

        if (debugOrientation)
        {
            Debug.Log($"[VRAssemblyOrientationHelper] 🔄 基础旋转计算:");
            Debug.Log($"   LookRotation结果: {targetRotation.eulerAngles}");
            Debug.Log($"   这会让+Z轴朝向: {targetRotation * Vector3.forward}");
        }

        // 如果使用-Z轴，需要额外旋转180度
        if (useNegativeZ)
        {
            Quaternion adjustment = Quaternion.Euler(0, 180, 0);
            targetRotation *= adjustment;

            if (debugOrientation)
            {
                Debug.Log($"[VRAssemblyOrientationHelper] 🔄 -Z轴调整:");
                Debug.Log($"   应用180度Y轴旋转");
                Debug.Log($"   调整后旋转: {targetRotation.eulerAngles}");
                Debug.Log($"   现在+Z轴朝向: {targetRotation * Vector3.forward}");
                Debug.Log($"   现在-Z轴朝向: {targetRotation * Vector3.back}");
            }
        }

        if (debugOrientation)
        {
            // 验证最终结果
            Vector3 finalForward = targetRotation * Vector3.forward;
            Vector3 finalBack = targetRotation * Vector3.back;
            Vector3 finalUp = targetRotation * Vector3.up;
            Vector3 finalRight = targetRotation * Vector3.right;

            Debug.Log($"[VRAssemblyOrientationHelper] ✅ 最终结果验证:");
            Debug.Log($"   最终旋转: {targetRotation.eulerAngles}");
            Debug.Log($"   最终+Z轴朝向: {finalForward}");
            Debug.Log($"   最终-Z轴朝向: {finalBack}");
            Debug.Log($"   最终+Y轴朝向: {finalUp}");
            Debug.Log($"   最终+X轴朝向: {finalRight}");

            // 计算最终对齐度
            Vector3 expectedDirection = useNegativeZ ? finalBack : finalForward;
            float finalAlignment = Vector3.Dot(expectedDirection, toCameraDirection);
            Debug.Log($"   装配面最终对齐度: {finalAlignment:F3}");

            if (finalAlignment > 0.9f)
            {
                Debug.Log($"   ✅ 对齐度良好");
            }
            else if (finalAlignment > 0.5f)
            {
                Debug.LogWarning($"   ⚠️ 对齐度一般");
            }
            else
            {
                Debug.LogError($"   ❌ 对齐度差");
            }

            Debug.Log("=== 装配面朝向计算完成 ===");
        }

        return targetRotation;
    }

    /// <summary>
    /// 自动检测最佳装配面朝向
    /// </summary>
    public Quaternion CalculateOptimalOrientationToCamera(Transform referencePoint, Transform camera)
    {
        if (referencePoint == null || camera == null)
        {
            Debug.LogError("[VRAssemblyOrientationHelper] 参考点或摄像机为空");
            return Quaternion.identity;
        }

        Vector3 refPointPosition = referencePoint.position;
        Vector3 cameraPosition = camera.position;
        Vector3 toCameraDirection = (cameraPosition - refPointPosition).normalized;

        // 测试不同的装配面方向
        Vector3[] testDirections = {
            referencePoint.forward,    // +Z
            -referencePoint.forward,   // -Z
            referencePoint.up,         // +Y
            -referencePoint.up,        // -Y
            referencePoint.right,      // +X
            -referencePoint.right      // -X
        };

        string[] directionNames = { "+Z", "-Z", "+Y", "-Y", "+X", "-X" };

        float bestDot = -1f;
        int bestIndex = 0;

        // 找到与摄像机方向最接近的装配面
        for (int i = 0; i < testDirections.Length; i++)
        {
            float dot = Vector3.Dot(testDirections[i], toCameraDirection);
            if (debugOrientation)
            {
                Debug.Log($"[VRAssemblyOrientationHelper] {directionNames[i]}方向点积: {dot:F3}");
            }

            if (dot > bestDot)
            {
                bestDot = dot;
                bestIndex = i;
            }
        }

        if (debugOrientation)
        {
            Debug.Log($"[VRAssemblyOrientationHelper] 选择最佳装配面: {directionNames[bestIndex]} (点积: {bestDot:F3})");
        }

        // 计算目标旋转
        Vector3 bestDirection = testDirections[bestIndex];
        Quaternion targetRotation = Quaternion.LookRotation(toCameraDirection, Vector3.up);

        // 根据选择的方向调整旋转
        if (bestIndex == 1) // -Z
        {
            targetRotation *= Quaternion.Euler(0, 180, 0);
        }
        else if (bestIndex == 2) // +Y
        {
            targetRotation *= Quaternion.Euler(-90, 0, 0);
        }
        else if (bestIndex == 3) // -Y
        {
            targetRotation *= Quaternion.Euler(90, 0, 0);
        }
        else if (bestIndex == 4) // +X
        {
            targetRotation *= Quaternion.Euler(0, 0, -90);
        }
        else if (bestIndex == 5) // -X
        {
            targetRotation *= Quaternion.Euler(0, 0, 90);
        }
        // bestIndex == 0 (+Z) 不需要额外调整

        if (debugOrientation)
        {
            Debug.Log($"[VRAssemblyOrientationHelper] 最终目标旋转: {targetRotation.eulerAngles}");
        }

        return targetRotation;
    }

    /// <summary>
    /// 测试当前装配面朝向
    /// </summary>
    [ContextMenu("测试装配面朝向")]
    public void TestAssemblyFaceOrientation()
    {
        Camera mainCamera = Camera.main;
        if (mainCamera == null)
        {
            Debug.LogError("未找到主摄像机");
            return;
        }

        AssemblyPart[] parts = FindObjectsOfType<AssemblyPart>();
        if (parts.Length == 0)
        {
            Debug.LogError("场景中没有装配零件");
            return;
        }

        Debug.Log("=== 测试装配面朝向 ===");

        foreach (var part in parts)
        {
            Transform refPoint = part.GetReferencePoint(0);
            
            Debug.Log($"\n--- 零件: {part.name} ---");
            
            // 测试简单朝向
            Quaternion simpleRotation = CalculateOrientationToCamera(refPoint, mainCamera.transform);
            Debug.Log($"简单朝向计算结果: {simpleRotation.eulerAngles}");
            
            // 测试自动检测朝向
            Quaternion optimalRotation = CalculateOptimalOrientationToCamera(refPoint, mainCamera.transform);
            Debug.Log($"最佳朝向计算结果: {optimalRotation.eulerAngles}");
        }

        Debug.Log("=== 测试完成 ===");
    }

    /// <summary>
    /// 应用朝向到装配根节点
    /// </summary>
    public void ApplyOrientationToAssemblyRoot(Transform assemblyRoot, Transform referencePoint, Transform camera, bool useOptimal = true)
    {
        if (assemblyRoot == null)
        {
            Debug.LogError("[VRAssemblyOrientationHelper] 装配根节点为空");
            return;
        }

        Quaternion targetRotation = useOptimal ? 
            CalculateOptimalOrientationToCamera(referencePoint, camera) :
            CalculateOrientationToCamera(referencePoint, camera);

        assemblyRoot.rotation = targetRotation;

        if (debugOrientation)
        {
            Debug.Log($"[VRAssemblyOrientationHelper] 已应用朝向到装配根节点: {targetRotation.eulerAngles}");
        }
    }
}
