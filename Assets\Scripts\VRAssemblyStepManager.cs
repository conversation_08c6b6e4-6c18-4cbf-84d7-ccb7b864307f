using UnityEngine;
using System.Collections;

/// <summary>
/// VR装配步骤管理器
/// 在播放装配动画前自动调整装配点朝向
/// </summary>
public class VRAssemblyStepManager : MonoBehaviour
{
    [Header("组件引用")]
    [SerializeField] private VRAssemblyPositioner positioner;

    [Header("朝向调整设置")]
    [SerializeField] private bool autoAdjustOrientation = true; // 是否自动调整朝向
    [SerializeField] private bool useNegativeZAxis = true; // 是否使用-Z轴朝向摄像机
    [SerializeField] private float delayBeforeAnimation = 0.5f; // 朝向调整完成后到动画开始的延迟

    [Header("调试设置")]
    [SerializeField] private bool showDebugInfo = true; // 显示调试信息

    void Start()
    {
        // 自动查找VRAssemblyPositioner组件
        if (positioner == null)
        {
            positioner = FindObjectOfType<VRAssemblyPositioner>();
            if (positioner == null)
            {
                Debug.LogError("[VRAssemblyStepManager] 未找到VRAssemblyPositioner组件！");
            }
            else
            {
                Debug.Log("[VRAssemblyStepManager] 自动找到VRAssemblyPositioner组件");
            }
        }

        if (showDebugInfo)
        {
            Debug.Log($"[VRAssemblyStepManager] 初始化完成");
            Debug.Log($"   自动调整朝向: {autoAdjustOrientation}");
            Debug.Log($"   使用-Z轴朝向摄像机: {useNegativeZAxis}");
            Debug.Log($"   动画前延迟: {delayBeforeAnimation}秒");
        }
    }

    /// <summary>
    /// 准备装配步骤：在播放动画前调整朝向
    /// </summary>
    /// <param name="targetPartName">目标零件名称</param>
    /// <param name="referencePointIndex">参考点索引</param>
    /// <returns>准备完成的协程</returns>
    public IEnumerator PrepareAssemblyStep(string targetPartName, int referencePointIndex)
    {
        if (showDebugInfo)
        {
            Debug.Log($"[VRAssemblyStepManager] 准备装配步骤");
            Debug.Log($"   目标零件: {targetPartName}");
            Debug.Log($"   参考点索引: {referencePointIndex}");
        }

        if (positioner == null)
        {
            Debug.LogError("[VRAssemblyStepManager] VRAssemblyPositioner组件未设置！");
            yield break;
        }

        if (autoAdjustOrientation)
        {
            // 执行第二部分功能：调整装配点朝向
            yield return positioner.AdjustOrientationForAssemblyStep(targetPartName, referencePointIndex, useNegativeZAxis);

            // 等待一段时间再开始动画
            if (delayBeforeAnimation > 0)
            {
                if (showDebugInfo)
                {
                    Debug.Log($"[VRAssemblyStepManager] 等待 {delayBeforeAnimation} 秒后开始动画");
                }
                yield return new WaitForSeconds(delayBeforeAnimation);
            }
        }

        if (showDebugInfo)
        {
            Debug.Log($"[VRAssemblyStepManager] 装配步骤准备完成，可以开始播放动画");
        }
    }

    /// <summary>
    /// 准备装配步骤的重载方法：支持自定义朝向设置
    /// </summary>
    /// <param name="targetPartName">目标零件名称</param>
    /// <param name="referencePointIndex">参考点索引</param>
    /// <param name="useNegativeZ">是否使用-Z轴朝向摄像机</param>
    /// <returns>准备完成的协程</returns>
    public IEnumerator PrepareAssemblyStep(string targetPartName, int referencePointIndex, bool useNegativeZ)
    {
        if (showDebugInfo)
        {
            Debug.Log($"[VRAssemblyStepManager] 准备装配步骤（自定义朝向）");
            Debug.Log($"   目标零件: {targetPartName}");
            Debug.Log($"   参考点索引: {referencePointIndex}");
            Debug.Log($"   使用-Z轴朝向: {useNegativeZ}");
        }

        if (positioner == null)
        {
            Debug.LogError("[VRAssemblyStepManager] VRAssemblyPositioner组件未设置！");
            yield break;
        }

        if (autoAdjustOrientation)
        {
            // 执行第二部分功能：调整装配点朝向
            yield return positioner.AdjustOrientationForAssemblyStep(targetPartName, referencePointIndex, useNegativeZ);

            // 等待一段时间再开始动画
            if (delayBeforeAnimation > 0)
            {
                if (showDebugInfo)
                {
                    Debug.Log($"[VRAssemblyStepManager] 等待 {delayBeforeAnimation} 秒后开始动画");
                }
                yield return new WaitForSeconds(delayBeforeAnimation);
            }
        }

        if (showDebugInfo)
        {
            Debug.Log($"[VRAssemblyStepManager] 装配步骤准备完成，可以开始播放动画");
        }
    }

    /// <summary>
    /// 设置自动调整朝向功能
    /// </summary>
    public void SetAutoAdjustOrientation(bool enabled)
    {
        autoAdjustOrientation = enabled;
        if (showDebugInfo)
        {
            Debug.Log($"[VRAssemblyStepManager] 自动调整朝向设置为: {(enabled ? "启用" : "禁用")}");
        }
    }

    /// <summary>
    /// 设置默认朝向轴
    /// </summary>
    public void SetDefaultOrientationAxis(bool useNegativeZ)
    {
        useNegativeZAxis = useNegativeZ;
        if (showDebugInfo)
        {
            Debug.Log($"[VRAssemblyStepManager] 默认朝向轴设置为: {(useNegativeZ ? "-Z轴" : "Z轴")}");
        }
    }

    /// <summary>
    /// 设置动画前延迟时间
    /// </summary>
    public void SetDelayBeforeAnimation(float delay)
    {
        delayBeforeAnimation = delay;
        if (showDebugInfo)
        {
            Debug.Log($"[VRAssemblyStepManager] 动画前延迟设置为: {delay} 秒");
        }
    }

    /// <summary>
    /// 获取当前配置
    /// </summary>
    public (bool autoAdjust, bool useNegativeZ, float delay) GetCurrentConfig()
    {
        return (autoAdjustOrientation, useNegativeZAxis, delayBeforeAnimation);
    }

    /// <summary>
    /// 显示当前配置信息
    /// </summary>
    [ContextMenu("显示配置信息")]
    public void ShowConfigInfo()
    {
        Debug.Log("=== VRAssemblyStepManager 配置信息 ===");
        Debug.Log($"   VRAssemblyPositioner: {(positioner != null ? "已连接" : "未连接")}");
        Debug.Log($"   自动调整朝向: {autoAdjustOrientation}");
        Debug.Log($"   使用-Z轴朝向摄像机: {useNegativeZAxis}");
        Debug.Log($"   动画前延迟: {delayBeforeAnimation} 秒");
        Debug.Log($"   调试信息显示: {showDebugInfo}");
        Debug.Log("=== 配置信息结束 ===");
    }

    /// <summary>
    /// 测试装配步骤准备功能
    /// </summary>
    [ContextMenu("测试装配步骤准备")]
    public void TestPrepareAssemblyStep()
    {
        // 使用示例数据进行测试
        StartCoroutine(PrepareAssemblyStep("LongUBracket", 0));
    }
}
